# Workflow 個人資料頁面

## 概述
根據提供的截圖，在 workflow 文件夾中創建了新的個人資料表單界面。

## 文件位置
- `src/views/workflow/profile.vue` - 主要的個人資料表單組件

## 界面結構

### 左列字段
1. **ID** - 10486538 (禁用)
2. **中文姓名 (userid)** - 202407966 (禁用)
3. **電郵** - <EMAIL>
4. **所屬部門及職位編號** - (空)
5. **電話號碼** - (空)
6. **手機號碼** - 90438327
7. **個人編號** - 020740e-a4dc-4b08-8971-411417c67e3 (禁用)
8. **姓名** - <PERSON> 2 3
9. **所屬部門及職位編號** - (空)
10. **中文姓名** - (空)

### 右列字段
1. **會員編號 (memberid)** - 10486538 (禁用)
2. **電郵重複確認時間** - 兩個輸入框 + 時間按鈕
3. **手機號碼重複確認時間** - 兩個輸入框 + 時間按鈕
4. **客戶類別** - 青年之友
5. **英文姓名** - Johnny Au 2
6. **性別** - 男 (下拉選擇)
7. **生日期** - (空)
8. **生日期** - (空)

## 路由配置
- 路徑: `/workflow-profile`
- 組件名: `WorkflowProfile`
- 菜單標題: "個人資料"

## 樣式特點
- 雙列布局
- 禁用字段使用灰色背景
- 時間確認字段有特殊的輸入組合（輸入框 + 時間按鈕）
- 統一的表單樣式

## 訪問方式
1. 啟動開發服務器
2. 登錄系統
3. 在左側菜單中點擊"個人資料"
4. 或直接訪問: `http://localhost:8081/workflow-profile`

## 注意事項
- 某些字段被設置為禁用狀態，符合截圖要求
- 時間確認字段使用了特殊的組合輸入設計
- 界面嚴格按照提供的截圖實現
- 沒有包含個人信息預覽功能，按要求省略
