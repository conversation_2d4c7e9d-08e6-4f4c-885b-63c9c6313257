<template>
  <img 
    :src="currentSrc" 
    @error="handleError" 
    :alt="alt"
  >
</template>

<script>
export default {
  props: {
    src: {
      type: String,
      required: false,
      default: null
    },
    fallback: {
      type: String,
      default: require('@/assets/pcm/card-detail/access_denied.jpg')
    },
    alt: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      errored: false
    }
  },
  computed: {
    currentSrc() {
      // 如果 src 为 null、undefined 或空字符串，或者图片加载出错，则使用 fallback
      return !this.src || this.errored ? this.fallback : this.src
    }
  },
  methods: {
    handleError() {
      this.errored = true
    }
  }
}
</script>