<template>
  <div class="top-bar">
    <i
      @click="drawerVisible = !drawerVisible"
      class="el-icon-s-unfold hidden-sm-and-up"
      :style="{ fontSize: '22px' }"
    ></i>
    <!-- 系统Logo -->
    <img src="@/assets/pcm/top-bar/logo.png" alt="Logo" class="logo" />

    <!-- 用户信息 -->
    <div class="user-info" v-if="'/login' != this.$route.path">
      <!-- 语言切换按钮 -->
      <button class="language-switch hidden-xs-only" @click="toggleLanguage">
        {{ $i18n.locale === "zh-TW" ? "English" : "繁體中文" }}
      </button>
      <!-- 三个图标 -->
      <div class="icons hidden-xs-only">
        <!-- 群组图标 -->
        <img
          :src="activeIcon === 'tool' ? toolActiveIcon : toolIcon"
          alt="Icon 1"
          class="icon"
          @click="navigateTo('/tool', 'recycleBin')"
        />
        <!-- 消息图标 -->
        <!-- <img
          :src="activeIcon === 'message' ? messageActiveIcon : messageIcon"
          alt="Icon 2"
          class="icon"
          @click="navigateTo('/message', 'pendingColleagues')"
        /> -->
        <!-- 任务图标 -->
        <!-- <img
          :src="activeIcon === 'quest' ? questActiveIcon : questIcon"
          alt="Icon 3"
          class="icon"
        /> -->
      </div>

      <!-- 竖线分割 -->
      <div class="divider hidden-xs-only"></div>
      <el-dropdown @command="handleCommand" trigger="click">
        <img
          :src="avatar"
          alt="User Avatar"
          class="avatar"
        />
        <el-dropdown-menu class="hidden-sm-and-up" slot="dropdown">
          <el-dropdown-item command="a">{{
            $t("personalInfo")
          }}</el-dropdown-item>
          <el-dropdown-item command="b">{{ $t("logout") }}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <span class="username hidden-xs-only" @click="toggleDropdown"
        >{{ user.username }}|{{ user.nickname }}</span
      >
      <img
        src="@/assets/pcm/top-bar/down.png"
        alt="Down Arrow"
        class="down-arrow hidden-xs-only"
        @click="toggleDropdown"
      />
      <!-- 绑定类名 -->
      <div
        class="dropdown"
        :class="{ 'dropdown--visible': isDropdownVisible }"
        v-if="isDropdownVisible"
      >
        <div
          class="dropdown-item"
          @click="navigateTo('/tool', 'changePassword')"
        >
          {{ $t("personalInfo") }}
        </div>
        <div class="dropdown-item" @click="logout">{{ $t("logout") }}</div>
      </div>
    </div>

    <!-- 未登录状态 -->
    <div class="user-info" v-else>
      <!-- 语言切换按钮 -->
      <button class="language-switch" @click="toggleLanguage">
        {{ $i18n.locale === "zh-TW" ? "English" : "繁體中文" }}
      </button>
      <img src="@/assets/logo/avatar.png" alt="Avatar" class="avatar" />
    </div>
    <el-drawer
      title="导航菜单"
      :visible.sync="drawerVisible"
      direction="ltr"
      :size="240"
      :with-header="false"
    >
      <div class="abs-class">
        <MenuBar @closeSideBarFn="drawerVisible = false" />
        <FixedBottomActions></FixedBottomActions>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import FixedBottomActions from "../../../components/FixedBottomActions/FixedBottomActions.vue";
import { getPath } from "@/utils/ruoyi";
import { getUserProfile } from "@/api/system/user";
import MenuBar from "@/layout/components/pcm/MenuBar.vue";
export default {
  name: "TopBar",
  components: {
    MenuBar,
    FixedBottomActions,
  },
  computed: {
    ...mapGetters([
      'avatar',
      'nickname',
    ]),
  },
  data() {
    return {
      user: {},
      isDropdownVisible: false,
      activeIcon: null, // 当前激活的图标
      toolIcon: require("@/assets/pcm/top-bar/group.png"), // 默认群组图标
      toolActiveIcon: require("@/assets/pcm/top-bar/group-active.png"), // 激活状态群组图标
      // messageIcon: require("@/assets/pcm/top-bar/message.png"), // 默认消息图标
      // messageActiveIcon: require("@/assets/pcm/top-bar/message-active.png"), // 激活状态消息图标
      questIcon: require("@/assets/pcm/top-bar/quest.png"), // 默认任务图标
      questActiveIcon: require("@/assets/pcm/top-bar/quest-active.png"), // 激活状态任务图标
      drawerVisible: false,
    };
  },
  mounted() {
    if ("/login" != this.$route.path) {
      this.getUser();
    }
    // 初始化时根据当前路由设置 activeIcon
    this.setActiveIconFromRoute();
    // 监听全局点击事件
    document.addEventListener("click", this.handleClickOutside);
  },
  beforeDestroy() {
    // 移除全局点击事件监听
    document.removeEventListener("click", this.handleClickOutside);
  },
  watch: {
    // 监听路由变化
    $route() {
      this.setActiveIconFromRoute();
    },
  },
  methods: {
    handleCommand(command) {
      if (command === "a") {
        this.navigateTo("/tool", "changePassword");
      } else {
        this.logout();
      }
    },
    toggleDropdown() {
      this.isDropdownVisible = !this.isDropdownVisible;
    },
    closeDropdown() {
      this.isDropdownVisible = false; // 关闭下拉菜单
    },
    handleClickOutside(event) {
      // 判断点击是否发生在下拉菜单外部
      if (this.isDropdownVisible && !this.$el.contains(event.target)) {
        this.closeDropdown();
      }
    },
    navigateTo(route, menuName) {
      this.isDropdownVisible = false;
      // 跳转到对应路由
      this.$router.push({
        path: route,
        query: {
          menuName: menuName,
        },
      });
    },
    setActiveIconFromRoute() {
      // 根据当前路由设置 activeIcon
      const route = this.$route.path;
      if (route === "/tool" && this.$route.query.menuName === "recycleBin") {
        this.activeIcon = "tool";
      }
      //  else if (route === "/message") {
      //   this.activeIcon = "message";
      // }
      else if (route === "/quest") {
        this.activeIcon = "quest";
      } else {
        this.activeIcon = null; // 如果路由不匹配，清空 activeIcon
      }
    },
    getUser() {
      getUserProfile().then((response) => {
        this.user = response.data;
      });
    },
    async logout() {
      this.isDropdownVisible = false;
      this.$modal
        .confirm(this.$t("confirmLogout"), this.$t("systemPrompt")) // 使用翻译
        .then(() => {
          this.$store.dispatch("LogOut").then(() => {
            location.href = getPath("/login");
          });
        })
        .catch(() => {});
    },
    // 切换语言
    toggleLanguage() {
      const currentLocale = this.$i18n.locale;
      const newLocale = currentLocale === "zh-TW" ? "en" : "zh-TW";
      this.$i18n.locale = newLocale;
      localStorage.setItem("locale", newLocale); // 保存用户选择的语言
    },
  },
};
</script>
<style scoped>
.abs-class {
  position: relative;
  height: 100%;
}
/* TopBar */
.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 32px;
  padding-right: 10px;
  background-color: #ffffff;
  height: 60px; /* 设置顶部栏高度 */
}

.logo {
  margin-top: 8px;
  object-fit: cover;
  width: 210px;
  height: 54px;
}

.icons {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-left: 20px; /* 与 Logo 的间距 */
}

.icon {
  width: 24px; /* 图标宽度 */
  height: 24px; /* 图标高度 */
  object-fit: cover;
  cursor: pointer;
}

.divider {
  width: 1px; /* 竖线宽度 */
  height: 10px; /* 竖线高度，缩短竖线 */
  background-color: #999999; /* 竖线颜色 */
  margin: 0 15px; /* 竖线与图标和头像的间距 */
  align-self: center; /* 竖线垂直居中 */
}

.avatar {
  width: 32px; /* 头像宽度 */
  height: 32px; /* 头像高度 */
  object-fit: cover;
  margin-right: 12px;
}

.user-info {
  display: flex;
  align-items: center; /* 让 user-info 内的内容垂直居中 */
}

.username {
  margin-left: 10px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  color: #1f1f1f;
}

.down-arrow {
  width: 16px; /* 箭头宽度 */
  height: 16px; /* 箭头高度 */
  margin-left: 8px; /* 箭头与用户名的间距 */
  cursor: pointer;
}

.dropdown {
  position: absolute;
  top: 60px; /* 下拉菜单距离顶部栏的距离 */
  right: 20px; /* 下拉菜单距离右侧的距离 */
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1;
  opacity: 0;
  transform: scaleY(0);
  transform-origin: top;
  transition: all 0.3s ease;
}

.dropdown::before {
  content: "";
  position: absolute;
  top: -8px;
  right: 20px;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid #ccc;
}

.dropdown::after {
  content: "";
  position: absolute;
  top: -7px;
  right: 20px;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid #fff;
}

.dropdown-item {
  padding: 10px 20px;
  cursor: pointer;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
}

/* 展开状态的样式 */
.dropdown--visible {
  opacity: 1;
  transform: scaleY(1);
  z-index: 2;
}

/* 语言切换按钮样式 */
.language-switch {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  color: #1f1f1f;
  margin-left: 15px; /* 与任务图标的间距 */
}

.language-switch:hover {
  color: #1ea235; /* 鼠标悬停时的颜色 */
}
</style>