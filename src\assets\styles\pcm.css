.el-button--mini {
    padding: 5px 8px;
    font-size: 12px;
    border-radius: 8px;
}

.button-icon{
    width: 16px!important;
    height: 16px!important;
}
.el-dialog{
    border-radius: 16px;
}

.el-button--success,.el-button--success:focus{
    background-color: #1EA235;
    border-color: #1EA235;
}

.el-checkbox__input.is-checked + .el-checkbox__label{
    color: #ED6C00;
}

.el-checkbox__input.is-checked .el-checkbox__inner {
    color: #ED6C00;
    border-color: #ED6C00;
    background-color: #ED6C00;
}

/* .el-button:hover, .el-button:focus{
    color: #ED6C00;
} */
.el-checkbox__input.is-indeterminate .el-checkbox__inner{
    color: #ED6C00;
    border-color: #ED6C00;
    background-color: #ED6C00;

}

.el-divider--horizontal{
    margin: 18px 0;
}

.el-tag.el-tag--info{
    background-color: #ffffff;
    border-color: #e9e9eb;
    color: #909399;
}

.el-tree-node__content{
    margin-top: 5px;
}
.el-input-group__append,
.el-input-group__prepend {
  background-color: #1ea235;
  color: #ffffff;
  font-size: 16px;
}

.el-input__prefix {
    display: flex;
    align-items: center;
  }

  .el-message-box {
    display: inline-block;
    width: 90vw;
    max-width: 420px;
    padding-bottom: 10px;
    vertical-align: middle;
    background-color: #FFFFFF;
    border-radius: 4px;
    border: 1px solid #e6ebf5;
    font-size: 16px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    text-align: left;
    overflow: hidden;
    backface-visibility: hidden;
    box-sizing: border-box;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-height: 80vh;
    overflow-y: auto;
}
