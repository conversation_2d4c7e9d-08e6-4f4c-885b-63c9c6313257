<!DOCTYPE html>
<html>
<head>
    <title>路由测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>路由配置测试</h1>
    <p class="info">请在浏览器中访问 <a href="http://localhost:8081/" target="_blank">http://localhost:8081/</a></p>
    
    <h2>预期结果：</h2>
    <ul>
        <li class="success">✓ 首页应该重定向到 /mycalendar</li>
        <li class="success">✓ 左侧菜单应该显示 "我的日历" 选项</li>
        <li class="success">✓ 点击 "我的日历" 应该显示日历组件</li>
        <li class="success">✓ 日历应该显示当前年月</li>
        <li class="success">✓ 日历应该有月份和年份选择器</li>
    </ul>
    
    <h2>如果遇到问题：</h2>
    <ul>
        <li class="error">如果看不到 "我的日历" 菜单项，可能是权限问题</li>
        <li class="error">如果首页没有重定向到日历，检查路由配置</li>
        <li class="error">如果日历显示异常，检查组件代码</li>
    </ul>
    
    <h2>测试步骤：</h2>
    <ol>
        <li>打开浏览器访问 http://localhost:8081/</li>
        <li>如果需要登录，请先登录</li>
        <li>检查左侧菜单是否有 "我的日历" 选项</li>
        <li>点击 "我的日历" 查看是否显示日历组件</li>
        <li>测试日历的月份和年份选择功能</li>
    </ol>
</body>
</html>
