<template>
  <div class="container">
    <!-- 上半部分 -->
    <div class="header">
      <el-row type="flex" align="middle" :gutter="10">
        <!-- 将这三个元素作为一个整体 -->
        <el-col :span="12" :xs="24" class="header-left-group">
          <div class="header-left-content">
            <el-dropdown
              trigger="click"
              @command="selectTag"
              placement="bottom-start"
              class="title-dropdown"
            >
              <div class="title">
                <span class="title-text">{{
                  currentTag || $t("allCards")
                }}</span>
                <i class="el-icon-arrow-down"></i>
              </div>
              <template #dropdown>
                <el-dropdown-menu class="tag-dropdown">
                  <el-dropdown-item :command="null" class="first-item">
                    {{ $t("allCards") }}
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-for="tag in tags"
                    :key="tag.id"
                    :command="tag"
                  >
                    {{ tag.name }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <el-input
              :placeholder="$t('searchHint')"
              v-model="queryParams.keyword"
              class="search-input hidden-xs-only"
            >
              <template #append>
                <el-button
                  @click="handleQuery"
                  slot="append"
                  type="success"
                  icon="el-icon-search"
                ></el-button>
              </template>
            </el-input>

            <span
              class="advanced-filter hidden-xs-only"
              @click="handleOpenSearchForm"
            >
              {{ $t("advancedFilter") }}
            </span>

            <span
              class="advanced-filter hidden-xs-only"
              @click="handleClearFilter"
            >
              {{ $t("clearFilter") }}
            </span>

            <div
              @click="handleOpenSearchForm"
              class="mobile-filter hidden-sm-and-up"
            >
              <span class="advanced-filter">
                {{ $t("advancedFilter") }}
              </span>
              <i
                class="el-icon-search"
                :style="{ fontSize: '20px', paddingLeft: '8px' }"
              ></i>
            </div>
          </div>
        </el-col>

        <el-col :span="12" class="header-right hidden-xs-only">
          <!-- <el-button
            @click="openUploadDialog"
            type="success"
            class="upload-button"
          >
            {{ $t("uploadCard") }}
          </el-button> -->
          <div class="upload-action">
            <!-- 按钮组合 -->
            <el-button-group>
              <!-- 主上传按钮 -->
              <el-button
                icon="el-icon-plus"
                type="success"
                @click="openUploadDialog"
                class="main-btn"
              >
                {{ $t("uploadCard") }}
              </el-button>

              <!-- 下拉触发按钮 -->
              <el-dropdown trigger="click" @command="handleMenuClick">
                <el-button type="success" class="dropdown-btn">
                  <i class="el-icon-arrow-down"></i>
                </el-button>
                <!-- 下拉菜单 -->
                <el-dropdown-menu slot="dropdown" class="custom-dropdown">
                  <el-dropdown-item
                    command="0"
                    v-hasPermi="['pcm:card:import']"
                  >
                    <i class="el-icon-document"></i> {{ $t("excelImport") }}
                  </el-dropdown-item>
                  <el-dropdown-item command="1" divided>
                    <i class="el-icon-edit"></i> {{ $t("manualInput") }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </el-button-group>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="divider"></div>

    <!-- 下半部分 -->
    <div class="content">
      <div class="summary">
        <span>{{ $t("total") }} {{ total }} {{ $t("businessCardsUnit") }}</span>
        <span class="operation hidden-xs-only">{{ $t("action") }}：</span>
        <el-button
          v-for="button in operationButtons"
          :key="button.label"
          size="mini"
          class="operation-button hidden-xs-only"
          @click="button.handler"
          v-if="!button.permission || $auth.hasPermi(button.permission)"
        >
          <div class="button-content">
            <img :src="button.icon" alt="" class="button-icon" />
            <span>{{ $t(button.label) }}</span>
          </div>
        </el-button>

        <div class="sort-select">
          <span class="operation">{{ $t("businessCardType") }}：</span>
          <el-select
            :style="{ width: '100px' }"
            v-model="queryParams.cardType"
            :placeholder="$t('selectCardType')"
          >
            <el-option label="所有名片" value="all"></el-option>
            <el-option label="我的名片" value="mine"></el-option>
            <el-option label="其他名片" value="other"></el-option>
          </el-select>

          <el-dropdown
            trigger="click"
            placement="bottom-end"
            @command="handleSortCommand"
          >
            <span class="time-sort">
              <i class="el-icon-sort" />
              {{
                currentSort === "time"
                  ? $t("sortByDate")
                  : currentSort === "name"
                  ? $t("sortByName")
                  : $t("sortByCompany")
              }}
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="time">{{
                  $t("sortByDate")
                }}</el-dropdown-item>
                <el-dropdown-item command="name">{{
                  $t("sortByName")
                }}</el-dropdown-item>
                <el-dropdown-item command="company">{{
                  $t("sortByCompany")
                }}</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <!-- 表格区域 -->
      <div class="table-container">
        <el-table
          empty-text="暫無數據"
          :data="tableData"
          style="width: 100%"
          v-loading="loading"
          :header-cell-style="{ background: '#1EA2350D' }"
          class="table"
          size="small"
          :border="true"
          :fit="true"
          height="calc(100%)"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
          :span-method="handleSpanMethod"
        >
          <el-table-column
            v-if="!$isMobile()"
            type="selection"
            width="55"
          ></el-table-column>
          <el-table-column
            v-if="!$isMobile()"
            prop="fullName"
            :label="$t('name')"
            width="120"
          >
            <template #default="scope">
              <div :style="{ lineHeight: '16px' }">
                <p>
                  {{ (scope.row.lastName ?? "") + (scope.row.firstName ?? "") }}
                </p>
                <p>
                  {{
                    (scope.row.firstNameEn ?? "") +
                    " " +
                    (scope.row.lastNameEn ?? "")
                  }}
                </p>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="imageUrl" :label="$t('cardImage')" width="150">
            <template #default="scope">
              <SafeImage
                :src="scope.row.imageUrl"
                alt="card-image"
                class="avatar"
              />
            </template>
          </el-table-column>
          <!-- 移动端show -->
          <el-table-column
            v-if="$isMobile()"
            prop="imageUrl"
            :label="$t('cardInformation')"
            width="180"
          >
            <template #default="scope">
              <div :style="{ lineHeight: '16px' }">
                <div>{{ scope.row.fullName }}</div>
                <div>
                  {{ scope.row.companyName || scope.row.companyNameEn }} <br />
                  {{ scope.row.jobTitle }}
                </div>
                <div class="contact-info"></div>
                <!-- 邮箱行 -->
                <div class="info-row">
                  <img
                    src="@/assets/pcm/card-table/email.png"
                    alt="Email Icon"
                    class="icon"
                  />
                  <span>{{ scope.row.email }}</span>
                </div>

                <!-- 手机号码行 -->
                <div class="info-row">
                  <img
                    src="@/assets/pcm/card-table/phone.png"
                    alt="Phone Icon"
                    class="icon"
                  />
                  <span>{{ scope.row.phoneMobile }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <!-- 移动端show -->
          <el-table-column
            v-if="!$isMobile()"
            :label="$t('companyPosition')"
            width="250"
          >
            <template #default="scope">
              <div>
                {{ scope.row.companyName || scope.row.companyNameEn }} <br />
                {{ scope.row.jobTitle }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-if="!$isMobile()"
            :label="$t('contactInfo')"
            min-width="150"
          >
            <template #default="scope">
              <div class="contact-info">
                <!-- 邮箱行 -->
                <div class="info-row">
                  <img
                    src="@/assets/pcm/card-table/email.png"
                    alt="Email Icon"
                    class="icon"
                  />
                  <span>{{ scope.row.email }}</span>
                </div>

                <!-- 手机号码行 -->
                <div class="info-row">
                  <img
                    src="@/assets/pcm/card-table/phone.png"
                    alt="Phone Icon"
                    class="icon"
                  />
                  <span>{{ scope.row.phoneMobile }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-if="!$isMobile()"
            :label="$t('creator')"
            width="150"
          >
            <template #default="scope">
              <div>
                {{ scope.row.creator }} <br />
                {{ parseTime(scope.row.createTime) }}
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column :label="$t('action')" width="100">
            <template #default>
              <span class="operation-dots">...</span>
            </template>
          </el-table-column> -->
        </el-table>
      </div>
      <div class="pagination">
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
    <FixedUploadCard
      @openCardModalEvent="mobileClientUploadCard"
      class="hidden-sm-and-up"
    ></FixedUploadCard>
    <!-- 显示标签选择框 -->
    <TagSelect
      ref="tagSelect"
      @confirm="batchUpdateTags"
      :initialSelectedTags="[]"
    ></TagSelect>

    <!-- 名片编辑人 -->
    <CreatorEdit ref="creatorEdit" @confirm="updateCreator"></CreatorEdit>

    <!-- 高级搜索弹窗 -->
    <SearchForm
      ref="searchForm"
      @confirm="searchList"
      @clear="handleClearFilter"
    ></SearchForm>

    <!-- 上传名片弹窗 -->
    <UploadForm ref="uploadForm"></UploadForm>
    <!-- 上传名片弹窗(移动端) -->
    <DrawerUpload ref="uploadFormMobile"></DrawerUpload>

    <excel-export-dialog
      ref="excelExportDialog"
      :approver="approver"
      :export-count="exportCount"
      :query-params="queryParams"
      :selected-ids="selectedIds"
    />
  </div>
</template>

<script>
import FixedUploadCard from "../../../components/FixedUploadCard/FixedUploadCard.vue";
import TagSelect from "./table/TagSelect.vue";
import CreatorEdit from "./table/CreatorEdit.vue";
import SearchForm from "./table/SearchForm.vue";
import UploadForm from "./table/UploadForm.vue";
import ExcelExportDialog from "./table/ExcelExportDialog";
import * as CardApi from "@/api/pcm/card";
import * as TagApi from "@/api/pcm/tag";
import { checkRestriction } from "@/api/pcm/approval";
import DrawerUpload from "./table/DrawerUpload.vue";

export default {
  components: {
    SearchForm,
    TagSelect,
    CreatorEdit,
    UploadForm,
    FixedUploadCard,
    DrawerUpload,
    ExcelExportDialog,
  },
  computed: {},
  watch: {
    "queryParams.cardType"(newVal, oldVal) {
      console.log("名片类型从", oldVal, "变为", newVal);
      this.handleQuery();
    },
  },
  created() {
    //从缓存加载高级查询参数
    const paramStr = sessionStorage.getItem("queryFilter");
    if (paramStr) {
      this.parentQueryParams = JSON.parse(paramStr);
      this.currentTag = this.getTagNameById(this.parentQueryParams.customTags);
    }
    this.getList();
    this.fetchTags(); // 获取标签数据
  },
  data() {
    return {
      drawer: false,
      loading: false,
      exportLoading: false,
      showTagDropdown: false, // 控制标签下拉菜单显示
      currentTag: null, // 当前选中的标签
      tags: [], // 标签列表
      // 总条数
      total: 0,
      parentQueryParams: {}, //高级查询条件参数
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        keyword: "",
        cardType: "all",
        orderBy: "createTime|desc", // 默认按时间排序
        customTags: null, // 新增标签筛选参数
      },
      currentSort: "time", // 默认按时间排序
      approver: "", // 审批人姓名
      exportCount: 0, // 导出数量
      operationButtons: [
        {
          label: "tag",
          icon: require("@/assets/pcm/card-table/tag.png"),
          handler: () => this.handleTag(),
        },
        {
          label: "delete",
          icon: require("@/assets/pcm/card-table/delete.png"),
          handler: () => this.handleDelete(),
        },
        {
          label: "modifyCreator",
          icon: require("@/assets/pcm/card-table/edit.png"),
          handler: () => this.handleEditCreator(),
        },
        {
          label: "exportExcel",
          icon: require("@/assets/pcm/card-table/export.png"),
          handler: () => this.handleExportCheck(),
          permission: "pcm:card:export", // 新增权限标识字段
        },
        {
          label: "mergeCards",
          icon: require("@/assets/pcm/card-table/merge.png"),
          handler: () => this.handleMerge(),
        },
      ],
      tableData: [],
      selectedIds: [],
    };
  },
  methods: {
    mobileClientUploadCard() {
      // this.drawer = true;
      this.$refs["uploadFormMobile"].open();
    },
    handleClose(done) {
      this.drawer = false;
    },
    // 获取标签列表
    fetchTags() {
      TagApi.getTagsAll().then((response) => {
        this.tags = response.data.list;
      });
    },

    // 选择标签
    selectTag(tag) {
      this.currentTag = tag ? tag.name : null;
      this.queryParams.customTags = tag ? tag.id : null;
      this.parentQueryParams.customTags = tag ? tag.id : null;
      //存入缓存
      sessionStorage.setItem(
        "queryFilter",
        JSON.stringify(this.parentQueryParams)
      );
      this.handleQuery(); // 重新查询数据
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /**
     * 重置初始化查詢條件
     */
    handleClearFilter() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        keyword: "",
        cardType: "all",
        orderBy: "createTime|desc", // 默认按时间排序
        customTags: null, // 新增标签筛选参数
      };
      this.currentTag = null;
      this.currentSort = "createTime|desc";
      this.parentQueryParams = {};
      sessionStorage.removeItem("queryFilter");
      this.getList();
    },
    getList() {
      this.loading = true;
      // 执行查询
      CardApi.getCardPage({
        ...this.queryParams,
        orderBy: this.queryParams.orderBy,
        ...this.parentQueryParams,
      }).then((response) => {
        this.tableData = response.data.list;
        this.total = response.data.total;
        this.loading = false;
        this.computeSpanData(); // 计算合并数据
      });
    },
    openUploadDialog() {
      this.$refs["uploadForm"].open();
    },
    handleRowClick(row, column, event) {
      this.$router.push({ name: "CardDetail", params: { cardId: row.id } });
    },
    handleOpenSearchForm() {
      this.$refs["searchForm"].open();
    },
    //高级查询
    searchList(param) {
      this.parentQueryParams = param;
      this.currentTag = this.getTagNameById(this.parentQueryParams.customTags);
      this.handleQuery(); // 重新查询数据
    },
    handleTag() {
      const ids = this.selectedIds;
      if (ids.length == 0) {
        this.$message.warning("請勾選要修改的數據！");
        return;
      }
      this.$refs["tagSelect"].open();
    },
    handleShare() {
      this.$message.info("点击了共享名片按钮");
    },
    handleDelete() {
      const ids = this.selectedIds;
      if (ids.length == 0) {
        this.$message.warning("請勾選要刪除的數據！");
        return;
      }
      this.$modal
        .confirm("是否確認刪除選取的名片資料?")
        .then(function () {
          return CardApi.deleteCard(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    handleEditCreator() {
      const ids = this.selectedIds;
      if (ids.length == 0) {
        this.$message.warning("請勾選要修改的數據！");
        return;
      }
      this.$refs["creatorEdit"].open();
    },
    batchUpdateTags(tags) {
      let param = {
        ids: this.selectedIds,
        fieldName: "customTags",
        fieldValue: tags.join(";"),
      };
      CardApi.batchUpdateField(param).then((res) => {
        this.$modal.msgSuccess("更新成功！");
        this.getList();
      });
    },
    handleMenuClick(type) {
      // 直接跳转到对应页面
      // this.$router.push(path)
      if (type === "0") {
        this.$router.push({
          path: "/tool",
          query: {
            menuName: "importExcel",
          },
        });
      }
      if (type === "1") {
        this.$router.push({
          name: "CardEdit",
        });
      }
    },
    updateCreator(creatorId) {
      let param = {
        ids: this.selectedIds,
        fieldName: "creator",
        fieldValue: creatorId,
      };
      CardApi.batchUpdateField(param).then((res) => {
        this.$modal.msgSuccess("更新成功！");
        this.getList();
      });
    },

    /**
     * 检查用户导出是否符合条件
     */
    handleExportCheck() {
      checkRestriction({
        ...this.queryParams,
        ids: this.selectedIds,
      })
        .then((res) => {
          if (res.data.isRestricted) {
            // 周期内超出50条，需要审批
            this.approver = res.data.approverName; // 设置审批人
            this.exportCount = res.data.exportTotal; // 设置导出数量

            this.$modal.confirm(res.data.msg).then(() => {
              this.$refs.excelExportDialog.open();
            });
          } else {
            this.handleExportExcel();
          }
        })
        .catch((error) => {
          console.error("检查导出限制出错:", error);
          this.$message.error("檢查導出權限出錯");
        });
    },

    handleExportExcel() {
      this.$modal
        .confirm("是否確認導出卡片數據項?")
        .then(() => {
          return CardApi.exportCardExcel({
            ...this.queryParams,
             ids: this.selectedIds,
            orderBy: this.queryParams.orderBy,
          });
        })
        .then((response) => {
          this.$download.excel(response, "卡片數據.xls");
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },
    handleMerge() {
      const ids = this.selectedIds;
      if (ids.length == 0) {
        this.$message.warning("請勾選要合併的數據！");
        return;
      }
      this.$modal
        .confirm("是否確認合併選中的名片資料項目?")
        .then(function () {
          return CardApi.merge(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("合併成功");
        })
        .catch(() => {});
    },
    handleSelectionChange(selection) {
      this.selectedIds = selection.map((item) => item.id);
    },
    handleSortCommand(command) {
      this.currentSort = command;
      switch (command) {
        case "time":
          this.queryParams.orderBy = "createTime|desc";
          break;
        case "name":
          this.queryParams.orderBy = "fullName|asc";
          break;
        case "company":
          this.queryParams.orderBy = "companyName|asc";
          break;
        default:
          this.queryParams.orderBy = "createTime|desc";
      }
      this.getList();
    },
    handleSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 只对 fullName 列进行合并 （只合并PC端）
      if (columnIndex === 1 && !this.$isMobile()) {
        // fullName 列的索引是 1（从 0 开始）
        const rowspan = this.spanArr[rowIndex];
        const colspan = rowspan > 0 ? 1 : 0;
        return {
          rowspan: rowspan,
          colspan: colspan,
        };
      }
    },
    //根据标签id查名称
    getTagNameById(id) {
      if (id == null || id === "") {
        return null;
      }
      const item = this.tags.find((tag) => tag.id === id);
      return item ? item.name : undefined; // 如果没找到返回 undefined
    },
    // 计算需要合并的行
    computeSpanData() {
      // 用于存储合并信息的数组
      this.spanArr = [];
      // 记录当前位置
      let pos = 0;
      // 遍历数据，计算相同 groupId 的行数
      for (let i = 0; i < this.tableData.length; i++) {
        if (i === 0) {
          // 第一行默认合并 1 行
          this.spanArr.push(1);
          pos = 0;
        } else {
          // 如果当前行的 groupId 和上一行相同
          if (this.tableData[i].groupId === this.tableData[i - 1].groupId) {
            // 上一行的合并数 +1
            this.spanArr[pos] += 1;
            // 当前行的合并数设为 0（表示不显示）
            this.spanArr.push(0);
          } else {
            // 不同 groupId，合并数为 1
            this.spanArr.push(1);
            pos = i;
          }
        }
      }
    },
  },
};
</script>

<style scoped>
.mobile-filter {
  display: flex;
  margin-left: auto;
}
/* 原有样式保持不变 */
.container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 16px;
  padding: 10px;
  box-sizing: border-box;
}
.header {
  flex-shrink: 0;
  margin-bottom: 10px;
}
.header-left-group {
  display: flex;
}

.header-left-content {
  display: flex;
  align-items: center;
  width: 100%;
  min-width: 0; /* 允许内容收缩 */
}

.title-dropdown {
  flex-shrink: 0; /* 防止标题区域被压缩 */
  margin-right: 10px;
}

.title {
  display: inline-flex;
  align-items: center;
  white-space: nowrap; /* 防止文字换行 */
  max-width: 200px; /* 设置最大宽度，超出显示省略号 */
  overflow: hidden;
  text-overflow: ellipsis;
}

.title-text {
  overflow: hidden;
  text-overflow: ellipsis;
}

.search-input {
  flex: 1; /* 占据剩余空间 */
  min-width: 200px; /* 设置最小宽度 */
  margin-right: 10px;
}

.advanced-filter {
  flex-shrink: 0; /* 防止被压缩 */
  color: #1ea235;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  white-space: nowrap;
  margin-left: 10px;
}
.title {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-weight: 500;
  font-size: 18px;
  position: relative; /* 为下拉菜单定位 */
  cursor: pointer; /* 显示可点击 */
}
.title span {
  margin-right: 5px;
}
.search-input {
  width: 100%;
}
.search-icon {
  width: 20px;
  height: 20px;
}
.header-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.advanced-filter {
  color: #1ea235;
  margin-left: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
}
.tip {
  background-color: #ed6c001a;
  padding: 8px;
  border-radius: 8px;
  margin-right: 10px;
  font-size: 13px;
  display: flex;
  height: 36px;
  align-items: center;
}
.close-tip {
  margin-left: 15px;
  color: #ed6c00;
  cursor: pointer;
}
.upload-button {
  background-color: #1ea235;
  color: #ffffff;
}
.divider {
  border-bottom: 1px solid #ccc;
  margin: 10px 0;
}
.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 隐藏内容区域溢出 */
}
.summary {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  font-size: 14px;
  font-weight: 400;
  padding: 5px 10px;
  background-color: #fff; /* 背景色与容器一致 */
  position: sticky;
  top: 0; /* 固定在顶部 */
  z-index: 10; /* 确保在表格上方 */
}
.operation {
  /* margin-left: 20px; */
  font-size: 14px;
  font-weight: 400;
}
.operation-button {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #0000001a;
  background-color: transparent;
  margin: 5px;
}
.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}
.button-icon {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}
.sort-select {
  margin-left: auto;
  gap: 10px;
  display: flex;
  align-items: center;
}
.table-container {
  flex: 1;
  position: relative; /* 为绝对定位的表格提供定位上下文 */
  overflow: hidden; /* 隐藏溢出 */
}
.table {
  position: absolute;
  width: 100%;
  height: 100%;
}
.avatar {
  width: 100px;
  height: 66px;
  margin-left: 15px;
  object-fit: contain;
}
/* 确保表头固定 */
.table-container /deep/ .el-table__header-wrapper {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #fff;
}

/* 数据区域滚动 */
.table-container /deep/ .el-table__body-wrapper {
  overflow-y: auto !important;
}
.contact-info {
  font-family: Arial, sans-serif;
}
.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 3px;
}
.icon {
  width: 16px;
  height: 16px;
  margin-right: 5px;
}
.operation-dots {
  cursor: pointer;
}
.pagination {
  display: flex;
  justify-content: flex-end;
}
.time-sort {
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
}

/* 新增标签下拉菜单样式 */
.tag-dropdown {
  min-width: 180px;
  max-height: 300px;
  overflow-y: auto;
  margin-left: 10px; /* 向右偏移 */
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.tag-dropdown .el-dropdown-item {
  padding: 10px 20px;
  font-size: 14px;
  color: #606266;
  transition: all 0.2s;
}

.tag-dropdown .el-dropdown-item:hover {
  background-color: #f5f7fa;
  color: #1ea235;
}

/* 第一个选项特殊样式 */
.tag-dropdown .first-item {
  font-weight: 500;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 4px;
}

/* 下拉箭头动画 */
.title i {
  transition: transform 0.2s;
}

.title .el-icon-arrow-down {
  margin-left: 4px;
}

/* 下拉菜单激活时箭头旋转 */
.el-dropdown .el-icon-arrow-down {
  transform: rotate(0);
}

.el-dropdown.is-active .el-icon-arrow-down {
  transform: rotate(180deg);
}

/* 按钮组容器 */
.upload-action {
  display: inline-block;
  margin: 10px 0;
}

/* 主按钮样式 */
.main-btn {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  padding: 10px 15px;
}

/* 下拉按钮样式 */
.dropdown-btn {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  padding: 10px 8px;
  border-left: 1px solid rgba(255, 255, 255, 0.3);
}

/* 自定义下拉菜单样式 */
.custom-dropdown {
  margin-top: 5px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 菜单项图标间距 */
.custom-dropdown .el-dropdown-menu__item i {
  margin-right: 8px;
  color: #606266;
}
</style>