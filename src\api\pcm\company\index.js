import request from '@/utils/request'

// 创建公司
export function createCompany(data) {
  return request({
    url: '/pcm/company/create',
    method: 'post',
    data: data
  })
}

// 更新公司
export function updateCompany(data) {
  return request({
    url: '/pcm/company/update',
    method: 'put',
    data: data
  })
}

// 删除公司
export function deleteCompany(id) {
  return request({
    url: '/pcm/company/delete?id=' + id,
    method: 'delete'
  })
}

// 获得公司
export function getCompany(id) {
  return request({
    url: '/pcm/company/get?id=' + id,
    method: 'get'
  })
}

// 获取所有子机构及名片
export function getCompanyTree(id) {
  return request({
    url: '/pcm/company/tree/' + id,
    method: 'get'
  })
}

// 获得公司分页
export function getCompanyPage(params) {
  return request({
    url: '/pcm/company/page',
    method: 'get',
    params
  })
}
// 导出公司 Excel
export function exportCompanyExcel(params) {
  return request({
    url: '/pcm/company/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 加载公司精简信息
 * @returns 
 */
export function listAllSimple(type){
  return request({
    url:'/pcm/company/list-all-simple?type=' + type,
    method:'get'
  })
}
