<script>
export default {
  name: "FixedBottomActions",
  methods: {
    navigateTo(route, menuName) {
      // 跳转到对应路由
      this.$router.push({
        path: route,
        query: {
          menuName: menuName,
        },
      });
    },
  },
};
</script>

<template>
  <div class="fixed-box-father">
    <div class="fixed-box-instance">
      <div class="flex-box">
        <div class="item">
          <img
            @click="navigateTo('/tool', 'recycleBin')"
            class="img-instance"
            src="@/assets/pcm/top-bar/group.png"
            alt=""
          />
        </div>
        <div class="item">
          <img
            class="img-instance"
            src="@/assets/pcm/top-bar/message.png"
            alt=""
          />
        </div>
        <div class="item">
          <img
            class="img-instance"
            src="@/assets/pcm/top-bar/quest.png"
            alt=""
          />
        </div>
      </div>
    </div>
    <div class="template-block"></div>
  </div>
</template>

<style lang="scss" scoped>
.fixed-box-instance {
  height: 120px;
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
  .flex-box {
    padding-left: 20px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    .item {
      width: 50px;
      height: 50px;
      // background-color: antiquewhite;
      .img-instance {
        width: 100%;
        height: 100%;
      }
    }
  }
}
.template-block {
  height: 120px;
}
</style>
