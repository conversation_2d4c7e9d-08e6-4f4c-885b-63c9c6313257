<template>
  <div class="settings-container">
    <!-- 左侧二级菜单 -->
    <div
      v-if="!$isMobile() || ($isMobile() && currentSetup === 1)"
      class="left-menu"
    >
      <div class="menu-title">{{ $t("Setting") }}</div>
      <div
        class="menu-item"
        :class="{ active: activeMenu === 'permission' }"
        @click="changeMenu('permission')"
      >
        <img :src="getIcon('permission')" class="menu-icon" />
        {{ $t("permissionManagement") }}
      </div>
      <div
        class="menu-item"
        :class="{ active: activeMenu === 'role' }"
        @click="changeMenu('role')"
      >
        <img :src="getIcon('role')" class="menu-icon" />
        {{ $t("roleManagement") }}
      </div>
      <div
        class="menu-item"
        :class="{ active: activeMenu === 'department' }"
        @click="changeMenu('department')"
      >
        <img :src="getIcon('department')" class="menu-icon" />
        {{ $t("departmentManagement") }}
      </div>
      <div
        class="menu-item"
        :class="{ active: activeMenu === 'employee' }"
        @click="changeMenu('employee')"
      >
        <img :src="getIcon('employee')" class="menu-icon" />
        {{ $t("staffManagement") }}
      </div>
      <div class="menu-divider"></div>
      <!-- <div
        class="menu-item"
        :class="{ active: activeMenu === 'mail-account' }"
        @click="changeMenu('mail-account')"
      >
        <img :src="getIcon('mail-account')" class="menu-icon" />
        {{ $t("emailAccount") }}
      </div>
      <div
        class="menu-item"
        :class="{ active: activeMenu === 'mail-template' }"
        @click="changeMenu('mail-template')"
      >
        <img :src="getIcon('mail-template')" class="menu-icon" />
        {{ $t("emailTemplate") }}
      </div>
      <div
        class="menu-item"
        :class="{ active: activeMenu === 'mail-log' }"
        @click="changeMenu('mail-log')"
      >
        <img :src="getIcon('mail-log')" class="menu-icon" />
        {{ $t("emailLogs") }}
      </div> -->

      <!-- 文件配置 -->
      <div
        class="menu-item"
        :class="{ active: activeMenu === 'file-config' }"
        @click="changeMenu('file-config')"
      >
        <img :src="getIcon('file-config')" class="menu-icon" />
        {{ $t("fileConfig") }}
      </div>

      <div
        class="menu-item"
        :class="{ active: activeMenu === 'file-log' }"
        @click="changeMenu('file-log')"
      >
        <img :src="getIcon('file-log')" class="menu-icon" />
        {{ $t("fileRecord") }}
      </div>

      <div
        class="menu-item"
        :class="{ active: activeMenu === 'api-success-log' }"
        @click="changeMenu('api-success-log')"
      >
        <img :src="getIcon('api-success-log')" class="menu-icon" />
        {{ $t("apiSuccessLogs") }}
      </div>

      <div
        class="menu-item"
        :class="{ active: activeMenu === 'api-error-log' }"
        @click="changeMenu('api-error-log')"
      >
        <img :src="getIcon('api-error-log')" class="menu-icon" />
        {{ $t("apiErrorLogs") }}
      </div>

      <div
        class="menu-item"
        :class="{ active: activeMenu === 'dict-type' }"
        @click="changeMenu('dict-type')"
      >
        <img :src="getIcon('dict-type')" class="menu-icon" />
        {{ $t("dictionaryType") }}
      </div>

      <div
        class="menu-item"
        :class="{ active: activeMenu === 'dict-data' }"
        @click="changeMenu('dict-data')"
      >
        <img :src="getIcon('dict-data')" class="menu-icon" />
        {{ $t("dictionaryData") }}
      </div>
    </div>

    <!-- 右侧内容 -->
    <div
      v-if="!$isMobile() || ($isMobile() && currentSetup === 2)"
      class="right-content"
    >
      <div class="content-title">
        <img
          src="@/assets/pcm/card-detail/back.png"
          alt="返回箭头"
          class="back-arrow hidden-sm-and-up"
          @click="goBack()"
        />
        <span>{{ activeMenuLabel }}</span>
      </div>
      <div class="content-divider"></div>
      <div class="content-body">
        <MenuList v-if="activeMenu === 'permission'" />

        <DeptList v-if="activeMenu === 'department'" />

        <RoleList v-if="activeMenu === 'role'" />

        <UserList v-if="activeMenu === 'employee'" />

        <!-- <MailAccount v-if="activeMenu === 'mail-account'" />

        <MailTemplate v-if="activeMenu === 'mail-template'" />

        <MailLog v-if="activeMenu === 'mail-log'" /> -->
        <FileConfig v-if="activeMenu === 'file-config'" />

        <FileLog v-if="activeMenu === 'file-log'" />

        <ApiSuccessLog v-if="activeMenu === 'api-success-log'" />

        <ApiErrorLog v-if="activeMenu === 'api-error-log'" />

        <DictType
          v-if="activeMenu === 'dict-type'"
          @toDictData="handleDictDataPage"
        />

        <DcitData v-show="activeMenu === 'dict-data'" ref="dictData" />
      </div>
    </div>
  </div>
</template>

<script>
import UserList from "@/views/pcm/setting/user";
import RoleList from "@/views/pcm/setting/role";
import DeptList from "@/views/pcm/setting/dept";
import MenuList from "@/views/pcm/setting/menu";

import MailAccount from "@/views/pcm/setting/mail/account";
import MailTemplate from "@/views/pcm/setting/mail/template";
import MailLog from "@/views/pcm/setting/mail/log";
import FileLog from "@/views/pcm/setting/file/index.vue";
import FileConfig from "@/views/pcm/setting/fileConfig/index.vue";
import ApiSuccessLog from "@/views/pcm/setting/apiAccessLog";
import ApiErrorLog from "@/views/pcm/setting/apiErrorLog";
import DictType from "@/views/pcm/setting/dict/index.vue";
import DcitData from "@/views/pcm/setting/dict/data.vue";
export default {
  components: {
    UserList,
    RoleList,
    DeptList,
    MenuList,
    MailAccount,
    MailTemplate,
    MailLog,
    ApiSuccessLog,
    ApiErrorLog,
    DictType,
    DcitData,
    FileLog,
    FileConfig,
  },
  data() {
    return {
      activeMenu: "permission", // 默认激活的菜单
      currentSetup: 1,
    };
  },
  computed: {
    activeMenuLabel() {
      switch (this.activeMenu) {
        case "permission":
          return this.$t("permissionManagement");
        case "role":
          return this.$t("roleManagement");
        case "department":
          return this.$t("departmentManagement");
        case "employee":
          return this.$t("staffManagement");
        // case "mail-account":
        //   return this.$t("emailAccount");
        // case "mail-template":
        //   return this.$t("emailTemplate");
        // case "mail-log":
        //   return this.$t("emailLogs");
        case "file-config":
          return this.$t("fileConfig");
        case "file-log":
          return this.$t("fileRecord");
        case "api-success-log":
          return this.$t("apiSuccessLogs");
        case "api-error-log":
          return this.$t("apiErrorLogs");
        case "dict-type":
          return this.$t("dictionaryType");
        case "dict-data":
          return this.$t("dictionaryData");
        default:
          return "";
      }
    },
  },
  methods: {
    goBack() {
      this.currentSetup = 1;
    },
    handleSelectSideMenuByMobileFn() {
      this.currentSetup = 2;
    },
    changeMenu(menu) {
      this.activeMenu = menu;
      if (this.$isMobile()) {
        this.handleSelectSideMenuByMobileFn();
      }
    },
    handleDictDataPage(dictType) {
      console.log(this.$refs["dictData"]);
      this.$refs["dictData"].queryParams.dictType = dictType;
      this.$refs["dictData"].getList();
      this.activeMenu = "dict-data";
    },
    getIcon(menu) {
      // 根据菜单项返回对应的图标路径
      switch (menu) {
        case "permission":
          return require("@/assets/pcm/setting/icon-permission.png");
        case "role":
          return require("@/assets/pcm/setting/icon-role.png");
        case "department":
          return require("@/assets/pcm/setting/icon-department.png");
        case "employee":
          return require("@/assets/pcm/setting/icon-employee.png");
        case "security":
          return require("@/assets/pcm/setting/icon-security.png");
        // case "mail-account":
        //   return require("@/assets/pcm/setting/icon-mail-account.png");
        // case "mail-template":
        //   return require("@/assets/pcm/setting/icon-mail-template.png");
        // case "mail-log":
        //   return require("@/assets/pcm/setting/icon-mail-log.png");
        case "file-config":
          return require("@/assets/pcm/setting/icon-file-config.png");
        case "file-log":
          return require("@/assets/pcm/setting/icon-file-log.png");
        case "api-success-log":
          return require("@/assets/pcm/setting/icon-success-log.png");
        case "api-error-log":
          return require("@/assets/pcm/setting/icon-error-log.png");
        case "dict-type":
          return require("@/assets/pcm/setting/icon-dict-type.png");
        case "dict-data":
          return require("@/assets/pcm/setting/icon-dict-data.png");
        default:
          return "";
      }
    },
  },
};
</script>

<style scoped>
.settings-container {
  display: flex;
  height: 100%;
  background-color: #f0f4fa;
}

.left-menu {
  flex: 2;
  background-color: white;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.menu-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 10px;
  font-size: 14px;
  color: #1f1f1f;
  font-weight: 400;
}

.menu-item:hover {
  background-color: #ed6c001a;
  color: #ed6c00;
}

.menu-item.active {
  background-color: #ed6c001a;
  color: #ed6c00;
}

.menu-icon {
  width: 16px;
  height: 16px;
  margin-right: 10px;
  filter: brightness(0) saturate(100%) invert(0%) sepia(0%) saturate(7500%)
    hue-rotate(0deg) brightness(100%) contrast(100%);
}

.menu-item:hover .menu-icon,
.menu-item.active .menu-icon {
  filter: brightness(0) saturate(100%) invert(50%) sepia(100%) saturate(1000%)
    hue-rotate(0deg) brightness(100%) contrast(100%);
}

.menu-divider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 20px 0;
}

.right-content {
  margin-left: 10px;
  background-color: white;
  padding: 20px;
  height: 100%;
  flex: 9; /* 占据剩余空间 */
  overflow-y: auto; /* 启用滚动条 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.back-arrow {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  margin-right: 8px;
  cursor: pointer;
}

@media screen and (max-width: 768px) {
  .right-content {
    margin-left: 0;
  }
  .content-title {
    text-align: center;
    position: relative;
  }
}

.content-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}

.content-divider {
  height: 1px;
  background-color: #e0e0e0;
  margin-bottom: 20px;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th,
td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

th {
  background-color: #f5f5f5;
}

button {
  padding: 6px 12px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: #0056b3;
}
</style>