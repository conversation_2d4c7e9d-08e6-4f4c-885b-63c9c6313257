<template>
  <div class="search-form-container">
    <base-dialog
      v-model="searchDialogVisible"
      title="advancedFilter"
      :confirmText="$t('startFilter')"
      width="520px"
      @confirm="handleSearchConfirm"
    >
      <!-- 自定义内容插槽 -->
      <template v-slot:content>
        <!-- 第一行：创建时间筛选 -->
        <div class="form-row">
          <label class="form-label">{{ $t("creationTimeFilter") }}</label>
        </div>

        <!-- 第二行：开始时间 -> 结束时间 -->
        <div class="form-row">
          <el-date-picker
            v-model="queryParam.createTime[0]"
            type="date"
            value-format="yyyy-MM-dd"
            :placeholder="$t('startTime')"
            size="small"
            class="date-picker"
          />
          <span class="time-separator">{{ $t("to") }}</span>
          <el-date-picker
            v-model="queryParam.createTime[1]"
            type="date"
            value-format="yyyy-MM-dd"
            :placeholder="$t('endTime')"
            size="small"
            class="date-picker"
          />
        </div>

        <!-- 第三行：名片创建人 -->
        <div class="form-row">
          <label class="form-label">{{ $t("cardCreator") }}</label>
        </div>

        <!-- 第四行：下拉多选框 -->
        <div class="form-row">
          <el-select
            v-model="queryParam.createUserId"
            multiple
            no-match-text="未搜尋到數據"
            filterable
            :placeholder="$t('selectCreator')"
            size="small"
            class="select-input"
          >
            <el-option
              v-for="creator in creators"
              :key="creator.id"
              :label="creator.nickname + '-' + creator.deptName"
              :value="creator.id"
            />
          </el-select>
        </div>

        <!-- 第五行：标签 -->
        <div class="form-row">
          <label class="form-label">{{ $t("tag") }}</label>
        </div>

        <!-- 第六行：标签单选下拉框 -->
        <div class="form-row">
          <el-select
            filterable
            v-model="queryParam.customTags"
            :placeholder="$t('selectTag')"
            size="small"
            no-match-text="未搜尋到數據"
            clearable
            class="select-input"
          >
            <el-option
              v-for="tag in tags"
              :key="tag.id"
              :label="tag.name"
              :value="tag.id"
            />
          </el-select>
        </div>

        <!-- 第七行：精准校验状态 -->
        <!-- <div class="form-row">
          <label class="form-label">精準校驗狀態</label>
        </div> -->

        <!-- 第八行：精准校验状态单选下拉框 -->
        <!-- <div class="form-row">
          <el-select
            v-model="validationStatus"
            placeholder="請選擇校驗狀態"
            size="small"
            class="select-input"
          >
            <el-option label="是" value="是" />
            <el-option label="否" value="否" />
          </el-select>
        </div> -->

        <!-- 第九行：CheckBox 组 -->
        <!-- <div class="form-row">
          <el-checkbox-group v-model="selectedOptions" size="small">
            <el-checkbox
              v-for="option in options"
              :key="option.value"
              :label="option.value"
              class="checkbox-item"
            >
              {{ option.label }}
            </el-checkbox>
          </el-checkbox-group>
        </div> -->

        <!-- 第十行：显示更多关键字 -->
        <div class="form-row">
          <el-button type="text" size="small" @click="toggleMoreKeywords">
            {{ showMoreKeywords ? $t("hideKeywords") : $t("showMoreKeywords") }}
          </el-button>
        </div>

        <!-- 更多关键字区域 -->
        <div v-if="showMoreKeywords" class="form-row">
          <div class="form-row">
            <label class="form-label">{{ $t("Company") }}</label>
          </div>
          <div class="form-row">
            <el-input
              v-model="queryParam.companyNames"
              :placeholder="$t('enterMoreKeywords')"
              size="small"
              class="keyword-input"
            />
          </div>

          <div class="form-row">
            <label class="form-label">{{ $t("position") }}</label>
          </div>
          <div class="form-row">
            <el-input
              v-model="queryParam.jobTitles"
              :placeholder="$t('enterMoreKeywords')"
              size="small"
              class="keyword-input"
            />
          </div>

          <div class="form-row">
            <label class="form-label">{{ $t("name") }}</label>
          </div>
          <div class="form-row">
            <el-input
              v-model="queryParam.fullNames"
              :placeholder="$t('enterMoreKeywords')"
              size="small"
              class="keyword-input"
            />
          </div>

          <div class="form-row">
            <label class="form-label">{{ $t("email") }}</label>
          </div>
          <div class="form-row">
            <el-input
              v-model="queryParam.emails"
              :placeholder="$t('enterMoreKeywords')"
              size="small"
              class="keyword-input"
            />
          </div>
        </div>
      </template>
      <template v-slot:header-right>
        <el-button type="text" @click="handleClear">
          <div class="button-content">
            <span>{{ $t("clearFilter") }}</span>
          </div>
        </el-button>
      </template>
    </base-dialog>
  </div>
</template>

<script>
import BaseDialog from "@/components/PcmDialog";
import { getTagsAll } from "@/api/pcm/tag";
import { listSimpleUsers } from "@/api/system/user";
export default {
  components: { BaseDialog },
  data() {
    return {
      searchDialogVisible: false,
      startTime: "", // 开始时间
      endTime: "", // 结束时间
      creators: [],
      tags: [],
      validationStatus: "", // 精准校验状态
      selectedOptions: [], // 选择的选项
      options: [
        { label: "郵箱", value: "email" },
        { label: "網址", value: "website" },
        { label: "手機", value: "phone" },
        { label: "電話", value: "telephone" },
        { label: "傳真", value: "fax" },
        { label: "圖片", value: "image" },
      ],
      showMoreKeywords: false, // 是否显示更多关键字
      queryParam: {
        createTime: ["", ""],
        createUserId: [], // 选择的创建人
        customTags: "", // 选择的标签
        fullNames: "",
        companyNames: "",
        jobTitles: "",
        emails: "",
      },
    };
  },
  mounted() {
    this.getTagList();
    this.getUserList();
  },
  methods: {
    async open() {
      //从缓存加载查询参数
      const paramStr = sessionStorage.getItem("queryFilter");
      if (paramStr) {
        this.queryParam = JSON.parse(paramStr);
      } else { //重置
        this.queryParam = {
          createTime: ["", ""],
          createUserId: [], // 选择的创建人
          customTags: "", // 选择的标签
          fullNames: "",
          companyNames: "",
          jobTitles: "",
          emails: "",
        };
      }
      this.searchDialogVisible = true;
    },
    toggleMoreKeywords() {
      this.showMoreKeywords = !this.showMoreKeywords;
    },
    handleSearchConfirm() {
      //存入缓存
      console.log(JSON.stringify(this.queryParam));
      sessionStorage.setItem("queryFilter", JSON.stringify(this.queryParam));
      this.$emit("confirm", this.queryParam);
      this.searchDialogVisible = false; // 关闭弹窗
    },

    handleClear() {
      this.$emit("clear");
      this.searchDialogVisible = false;
    },

    // 获取标签列表
    async getTagList() {
      // 执行查询
      getTagsAll().then((response) => {
        this.tags = response.data.list;
      });
    },

    // 获取标签列表
    async getUserList() {
      // 执行查询
      listSimpleUsers().then((response) => {
        console.log(response.data);
        this.creators = response.data;
      });
    },
  },
};
</script>

<style scoped>
.search-form-container {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.form-row {
  margin-bottom: 16px;
}

.form-row /deep/ .el-button--text {
  color: #000000;
  font-size: 14px;
  font-weight: 400;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
  display: block;
}

.date-picker {
}

.time-separator {
  margin: 0 8px;
  font-size: 14px;
  color: #606266;
}

.select-input {
  width: 100%;
}

.radio-item {
  margin-right: 16px;
}

.checkbox-item {
  margin-right: 16px;
}

.keyword-input {
  width: 100%;
  color: #000000;
}
</style>