<template>
  <div>
    <base-dialog
      v-model="deptDialogVisible"
      title="selectDepartment"
      @confirm="handleConfirm"
    >
      <template v-slot:content>
        <div class="dept-selector">
          <div class="dept-list-container">
            <div class="dept-list" ref="deptList">
              <div
                v-for="dept in filteredDepts"
                :key="dept.id"
                class="dept-item"
                :class="{ selected: isSelected(dept.id) }"
                @click="toggleSelect(dept)"
              >
                <div class="dept-icon">
                  <i class="el-icon-office-building"></i>
                </div>
                <div class="dept-info">
                  <div class="dept-name">{{ dept.name }}</div>
                  <div class="dept-parent" v-if="dept.parentId !== 0">
                    上級部門: {{ getParentName(dept.parentId) }}
                  </div>
                </div>
                <div class="selection-indicator" v-if="isSelected(dept.id)">
                  <i class="el-icon-check"></i>
                </div>
              </div>
            </div>
          </div>

          <div class="list-footer">
            <el-checkbox v-model="showOnlySelected">
               {{$t('viewSelectedOnly')}} ({{ selectedDepts.length }})
            </el-checkbox>
          </div>
        </div>
      </template>
    </base-dialog>
  </div>
</template>

<script>
import BaseDialog from "@/components/PcmDialog";
import { listSimpleDepts } from "@/api/system/dept"; 

export default {
  components: { BaseDialog },
  props: {
    selectedIds: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      deptDialogVisible: false,
      depts: [], // 部门列表数据
      selectedDeptIds: [], // 当前选中的部门ID
      showOnlySelected: false,
    };
  },
  computed: {
    filteredDepts() {
      if (this.showOnlySelected) {
        return this.depts.filter(dept => this.selectedDeptIds.includes(dept.id));
      }
      return this.depts;
    },
    selectedDepts() {
      return this.depts.filter(dept => this.selectedDeptIds.includes(dept.id));
    },
    parentDeptMap() {
      const map = {};
      this.depts.forEach(dept => {
        map[dept.id] = dept;
      });
      return map;
    }
  },
  watch: {
    selectedIds: {
      immediate: true,
      handler(newVal) {
        this.selectedDeptIds = [...newVal];
      }
    }
  },
  methods: {
    async open() {
      this.deptDialogVisible = true;
      if (this.depts.length === 0) {
        await this.fetchDepts();
      }
    },
    
    async fetchDepts() {
      listSimpleDepts().then(res => {
        this.depts = res.data;
      });
    },
    
    handleConfirm() {
      this.$emit('confirm', this.selectedDeptIds);
      this.deptDialogVisible = false;
    },
    
    toggleSelect(dept) {
      const index = this.selectedDeptIds.indexOf(dept.id);
      if (index === -1) {
        this.selectedDeptIds.push(dept.id);
      } else {
        this.selectedDeptIds.splice(index, 1);
      }
    },
    
    isSelected(deptId) {
      return this.selectedDeptIds.includes(deptId);
    },
    
    getParentName(parentId) {
      if (parentId === 0) return '無';
      const parentDept = this.parentDeptMap[parentId];
      return parentDept ? parentDept.name : '未知部門';
    },
    
    clearSelected() {
      this.selectedDeptIds = [];
    }
  }
};
</script>

<style scoped>
.dept-selector {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.dept-list-container {
  flex: 1;
  overflow: hidden;
}

.dept-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
  padding: 12px;
  overflow-y: auto;
  max-height: 400px;
}

.dept-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #ebeef5;
  position: relative;
}

.dept-item:hover {
  background-color: #f5f7fa;
}

.dept-item.selected {
  background-color: #ecf5ff;
  border-color: #c0e3ff;
}

.dept-icon {
  margin-right: 12px;
  color: #409eff;
  font-size: 20px;
}

.dept-info {
  flex: 1;
  min-width: 0;
}

.dept-name {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dept-parent {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.selection-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  color: #409eff;
  font-size: 14px;
  background: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.list-footer {
  padding: 12px;
  border-top: 1px solid #ebeef5;
}
</style>