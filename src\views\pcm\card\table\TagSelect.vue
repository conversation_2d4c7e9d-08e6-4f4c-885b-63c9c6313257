<template>
  <div>
    <base-dialog
      v-model="tagDialogVisible"
      title="selectTag"
      @confirm="handleConfirm"
    >
      <template v-slot:content>
        <div class="checkbox-list-container">
          <div class="search-box">
            <el-input
              v-model="searchKeyword"
              placeholder="輸入標籤名"
              clearable
              @input="handleSearch"
            />
          </div>
          <div class="checkbox-list">
            <div v-for="item in filteredItems" :key="item.id" class="list-item">
              <el-checkbox v-model="item.checked">{{ item.name }}</el-checkbox>
            </div>
          </div>
        </div>
      </template>
      <template v-slot:header-right>
        <el-button type="text" @click="handleTagPage">
          <div class="button-content">
            <img
              src="@/assets/pcm/card-table/g_tag.png"
              alt=""
              class="button-icon"
            />
            <span>{{ $t("companyTagManagement") }}</span>
          </div>
        </el-button>
      </template>
    </base-dialog>
  </div>
</template>

<script>
import BaseDialog from "@/components/PcmDialog";
import * as TagApi from "@/api/pcm/tag";

export default {
  components: { BaseDialog },
  props: {
    // 父组件传入的已选标签
    initialSelectedTags: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      tagDialogVisible: false, // 标签弹窗
      searchKeyword: "", // 搜索关键字
      items: [], // 标签列表
      selectedTags: [], // 选中的标签
    };
  },
  computed: {
    // 根据搜索关键字过滤列表项
    filteredItems() {
      return this.items.filter((item) =>
        item.name.toLowerCase().includes(this.searchKeyword.toLowerCase())
      );
    },
  },
  created() {
    this.getList();
  },
  methods: {
    async open() {
      this.tagDialogVisible = true;
      this.searchKeyword = "";
      this.clearSelectedTags(); // 清空选中的标签
      this.updateSelectedTags(this.initialSelectedTags)
    },
    handleSearch() {
      // 这里可以根据需要添加防抖逻辑
    },
    // 处理确认事件
    handleConfirm() {
      // 清空已选标签
      this.selectedTags = [];
      // 遍历 items，将选中的标签添加到 selectedTags
      this.items.forEach((item) => {
        if (item.checked) {
          this.selectedTags.push(item.id); //
        }
      });
      // 将选中的标签传递给父组件
      this.$emit("confirm", this.selectedTags);
      this.tagDialogVisible = false; // 关闭弹窗
    },
    // 清空选中的标签
    clearSelectedTags() {
      this.selectedTags = []; // 清空选中的标签
      this.items.forEach((item) => {
        item.checked = false; // 重置每个标签的选中状态
      });
    },
    // 处理右上角按钮点击事件
    handleTagPage() {
      this.$router.push({
        path: "/tool",
        query: {
          menuName: "companyTag",
        },
      });
    },
    /**
     * 更新标签选中状态
     * @param {Array} selectedIds - 需要选中的标签ID数组
     */
    updateSelectedTags(selectedIds) {
      console.log("父组件传入select_ids-->"+selectedIds)
      this.items = this.items.map((item) => ({
        ...item,
        checked: selectedIds.includes(String(item.id)),
      }));
    },
    // 获取标签列表
    async getList() {
      // 执行查询
      TagApi.getTagsAll().then((response) => {
        const res = response.data.list;
        this.items = res.map((item) => ({
          ...item, // 保留 API 返回的其他属性
          checked: this.initialSelectedTags.includes(String(item.id)), // 如果标签已选中，设置为 true
        }));
      });
    },
  },
};
</script>

<style scoped>
.checkbox-list-container {
  border-radius: 4px;
  overflow: hidden;
}

.search-box {
  padding: 10px;
}

.checkbox-list {
  max-height: 324px;
  overflow-y: auto;
}

.list-item {
  padding: 10px;
  border-bottom: 1px solid #e8e8e8;
}

.list-item:last-child {
  border-bottom: none;
}
</style>