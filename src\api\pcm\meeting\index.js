import request from '@/utils/request'

// 创建商谈记录表，存储名片相关的商谈记录
export function createMeeting(data) {
  return request({
    url: '/pcm/meeting/create',
    method: 'post',
    data: data
  })
}

// 更新商谈记录表，存储名片相关的商谈记录
export function updateMeeting(data) {
  return request({
    url: '/pcm/meeting/update',
    method: 'put',
    data: data
  })
}

// 删除商谈记录表，存储名片相关的商谈记录
export function deleteMeeting(id) {
  return request({
    url: '/pcm/meeting/delete?id=' + id,
    method: 'delete'
  })
}

// 获得商谈记录表，存储名片相关的商谈记录
export function getMeeting(id) {
  return request({
    url: '/pcm/meeting/get?id=' + id,
    method: 'get'
  })
}

// 获得商谈记录表，存储名片相关的商谈记录分页
export function getMeetingPage(params) {
  return request({
    url: '/pcm/meeting/page',
    method: 'get',
    params
  })
}

//根据名片id获取会议记录
export function getMeetingByCardId(cardId) {
  return request({
    url: '/pcm/meeting/getMeetingByCardId?cardId=' + cardId,
    method: 'get'
  })
}
// 导出商谈记录表，存储名片相关的商谈记录 Excel
export function exportMeetingExcel(params) {
  return request({
    url: '/pcm/meeting/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

//根据用户ID统计商谈记录数
export function countMeetingByUserId(userId,cardId) {
  return request({
    url: '/pcm/meeting/countMeetingByUserId',
    method: 'post',
    data:{userId:userId,cardId:cardId}
  })
}
