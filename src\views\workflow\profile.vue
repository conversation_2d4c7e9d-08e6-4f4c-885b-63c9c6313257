<template>
  <div class="profile-form-container">
    <el-form 
      ref="profileForm" 
      :model="formData" 
      label-width="140px"
      class="profile-form"
    >
      <el-row :gutter="20">
        <!-- 左列 -->
        <el-col :span="12">
          <!-- ID -->
          <el-form-item label="ID">
            <el-input 
              v-model="formData.id" 
              placeholder="10486538"
              disabled
              class="disabled-input"
            />
          </el-form-item>
          
          <!-- 中文姓名 (userid) -->
          <el-form-item label="中文姓名 (userid)">
            <el-input 
              v-model="formData.chineseNameUserid" 
              placeholder="202407966"
              disabled
              class="disabled-input"
            />
          </el-form-item>
          
          <!-- 電郵 -->
          <el-form-item label="電郵">
            <el-input 
              v-model="formData.email" 
              placeholder="<EMAIL>"
            />
          </el-form-item>
          
          <!-- 所屬部門及職位編號 -->
          <el-form-item label="所屬部門及職位編號">
            <el-input 
              v-model="formData.deptPositionCode1" 
              placeholder=""
            />
          </el-form-item>
          
          <!-- 電話號碼 -->
          <el-form-item label="電話號碼">
            <el-input 
              v-model="formData.phoneNumber" 
              placeholder=""
            />
          </el-form-item>
          
          <!-- 手機號碼 -->
          <el-form-item label="手機號碼">
            <el-input 
              v-model="formData.mobileNumber" 
              placeholder="90438327"
            />
          </el-form-item>
          
          <!-- 個人編號 -->
          <el-form-item label="個人編號">
            <el-input 
              v-model="formData.personalId" 
              placeholder="020740e-a4dc-4b08-8971-411417c67e3"
              disabled
              class="disabled-input"
            />
          </el-form-item>
          
          <!-- 姓名 -->
          <el-form-item label="姓名">
            <el-input 
              v-model="formData.name" 
              placeholder="Johnny Au 2 3"
            />
          </el-form-item>
          
          <!-- 所屬部門及職位編號 -->
          <el-form-item label="所屬部門及職位編號">
            <el-input 
              v-model="formData.deptPositionCode2" 
              placeholder=""
            />
          </el-form-item>
          
          <!-- 中文姓名 -->
          <el-form-item label="中文姓名">
            <el-input 
              v-model="formData.chineseName" 
              placeholder=""
            />
          </el-form-item>
        </el-col>
        
        <!-- 右列 -->
        <el-col :span="12">
          <!-- 會員編號 (memberid) -->
          <el-form-item label="會員編號 (memberid)">
            <el-input 
              v-model="formData.memberId" 
              placeholder="10486538"
              disabled
              class="disabled-input"
            />
          </el-form-item>
          
          <!-- 電郵重複確認時間 -->
          <el-form-item label="電郵重複確認時間">
            <div class="time-input-group">
              <el-input 
                v-model="formData.emailConfirmTime1" 
                placeholder=""
                class="time-input"
              />
              <el-button icon="el-icon-time" class="time-button"></el-button>
              <el-input 
                v-model="formData.emailConfirmTime2" 
                placeholder=""
                class="time-input"
              />
              <el-button icon="el-icon-time" class="time-button"></el-button>
            </div>
          </el-form-item>
          
          <!-- 手機號碼重複確認時間 -->
          <el-form-item label="手機號碼重複確認時間">
            <div class="time-input-group">
              <el-input 
                v-model="formData.mobileConfirmTime1" 
                placeholder=""
                class="time-input"
              />
              <el-button icon="el-icon-time" class="time-button"></el-button>
              <el-input 
                v-model="formData.mobileConfirmTime2" 
                placeholder=""
                class="time-input"
              />
              <el-button icon="el-icon-time" class="time-button"></el-button>
            </div>
          </el-form-item>
          
          <!-- 客戶類別 -->
          <el-form-item label="客戶類別">
            <el-input 
              v-model="formData.customerCategory" 
              placeholder="青年之友"
            />
          </el-form-item>
          
          <!-- 英文姓名 -->
          <el-form-item label="英文姓名">
            <el-input 
              v-model="formData.englishName" 
              placeholder="Johnny Au 2"
            />
          </el-form-item>
          
          <!-- 性別 -->
          <el-form-item label="性別">
            <el-select 
              v-model="formData.gender" 
              placeholder="男"
              style="width: 100%"
            >
              <el-option label="男" value="male"></el-option>
              <el-option label="女" value="female"></el-option>
            </el-select>
          </el-form-item>
          
          <!-- 生日期 -->
          <el-form-item label="生日期">
            <el-input 
              v-model="formData.birthday1" 
              placeholder=""
            />
          </el-form-item>
          
          <!-- 生日期 -->
          <el-form-item label="生日期">
            <el-input 
              v-model="formData.birthday2" 
              placeholder=""
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "WorkflowProfile",
  data() {
    return {
      formData: {
        id: '10486538',
        chineseNameUserid: '202407966',
        email: '<EMAIL>',
        deptPositionCode1: '',
        phoneNumber: '',
        mobileNumber: '90438327',
        personalId: '020740e-a4dc-4b08-8971-411417c67e3',
        name: 'Johnny Au 2 3',
        deptPositionCode2: '',
        chineseName: '',
        memberId: '10486538',
        emailConfirmTime1: '',
        emailConfirmTime2: '',
        mobileConfirmTime1: '',
        mobileConfirmTime2: '',
        customerCategory: '青年之友',
        englishName: 'Johnny Au 2',
        gender: 'male',
        birthday1: '',
        birthday2: ''
      }
    };
  },
  methods: {
    // 可以添加保存等方法
  }
};
</script>

<style scoped>
.profile-form-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.profile-form {
  background-color: white;
  padding: 30px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.disabled-input ::v-deep .el-input__inner {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}

.time-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-input {
  flex: 1;
}

.time-button {
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  background-color: #f5f7fa;
  color: #909399;
}

.time-button:hover {
  background-color: #e6e8eb;
}

/* 表單項樣式調整 */
.profile-form ::v-deep .el-form-item {
  margin-bottom: 18px;
}

.profile-form ::v-deep .el-form-item__label {
  color: #606266;
  font-weight: normal;
  line-height: 32px;
}

.profile-form ::v-deep .el-input__inner {
  height: 32px;
  line-height: 32px;
}

.profile-form ::v-deep .el-select .el-input__inner {
  height: 32px;
  line-height: 32px;
}
</style>
