<template>
  <div class="schedule-container" :class="{ 'pt-theme': isPTUser, 'unit-theme': !isPTUser }">
    <!-- 页面标题和状态标签 -->
    <div class="header-section">
      <div class="title-area">
        <h2>我的輪值表</h2>
        <el-button type="warning" icon="el-icon-location" class="check-in-btn" @click="goToAttendance">
          簽到/簽走
        </el-button>
      </div>

      <!-- 状态标签 -->
      <div class="status-tags">
        <el-tag class="custom-gray-tag" size="medium">已完成簽到紀錄</el-tag>
        <el-tag class="custom-yellow-tag" size="medium">未完成簽到紀錄</el-tag>
        <el-tag class="custom-green-tag" size="medium">未開始的工作時段</el-tag>
      </div>
    </div>

    <!-- 导航和视图切换 -->
    <div class="navigation-section">
      <div class="date-navigation">
        <el-button icon="el-icon-arrow-left" @click="previousPeriod" circle size="small"></el-button>
        <span class="current-period">{{ currentPeriodText }}</span>
        <el-button icon="el-icon-arrow-right" @click="nextPeriod" circle size="small"></el-button>
      </div>

      <div class="view-switcher">
        <el-radio-group v-model="viewMode" size="small">
          <el-radio-button label="month">月</el-radio-button>
          <el-radio-button label="week">周</el-radio-button>
          <el-radio-button label="list">列表</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 日历视图 -->
    <div class="calendar-section">
      <!-- 月视图 -->
      <div v-if="viewMode === 'month'" class="month-view">
        <el-calendar v-model="selectedDate" :first-day-of-week="1">
          <template slot="dateCell" slot-scope="{date, data}">
            <div class="calendar-day" :class="getDayClass(date, data)">
              <div class="day-number">
                {{ parseInt(data.day.split('-').slice(-1)[0]) }}日
                <span v-if="isToday(date.toISOString().split('T')[0])" class="today-label">今天</span>
              </div>
              <div class="day-content">
                <div v-for="schedule in getSchedulesForDate(date)" :key="schedule.id"
                     class="schedule-item" :class="schedule.status">
                  <div class="schedule-text">{{ schedule.timeRange }} {{ schedule.location }}</div>
                </div>
              </div>
            </div>
          </template>
        </el-calendar>
      </div>

        <!-- 周视图 -->
        <div v-if="viewMode === 'week'" class="week-view">
        <div class="week-header">
          <div class="time-column"></div>
          <div v-for="day in weekDays" :key="day.date" class="day-column">
            <div class="day-name">{{ day.name }}</div>
            <div class="day-date" :class="{ 'today': day.isToday }">{{ day.date }}</div>
          </div>
        </div>
        <div class="week-content">
          <div class="time-slots">
            <div v-for="hour in timeSlots" :key="hour" class="time-slot">
              <div class="time-label">{{ hour }}:00</div>
              <div class="hour-row">
                <div v-for="day in weekDays" :key="day.date" class="day-cell">
                  <div v-for="schedule in getSchedulesForDateTime(day.date, hour)"
                       :key="schedule.id" class="schedule-block" :class="schedule.status">
                    <div class="schedule-title">{{ schedule.location }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <div v-if="viewMode === 'list'" class="list-view">
        <div v-for="dateGroup in groupedSchedules" :key="dateGroup.date" class="date-group">
          <div class="date-header">
            <span class="date-text">{{ formatDateForList(dateGroup.date) }}</span>
            <span class="weekday-text">{{ getWeekday(dateGroup.date) }}</span>
          </div>
          <div class="schedules-list">
            <div v-for="schedule in dateGroup.schedules" :key="schedule.id" class="schedule-row">
              <div class="time-range">{{ schedule.timeRange }}</div>
              <div class="schedule-details">
                <span class="schedule-label">{{ schedule.description }}</span>
                <span class="time-display">{{ schedule.timeRange }}</span>
                <span class="participant-count">({{ getParticipantCount(schedule) }}小時)</span>
              </div>
            </div>
          </div>
        </div>
      </div>



    </div>
  </div>
</template>

<script>
export default {
  name: 'MyCalendar',
  data() {
    return {
      // 用户类型：PT或Unit
      isPTUser: true, // 这里可以从用户信息中获取

      // 视图模式
      viewMode: 'month', // month, list, week

      // 当前选中的日期
      selectedDate: new Date(2025, 9, 9), // 2025年10月9日

      // 模拟的排班数据
      schedules: [
        // 10月1日 - 已完成签到且PT已确认工作时数（灰色）
        {
          id: 1,
          date: '2025-10-01',
          timeRange: '09:00-13:00',
          location: 'ABC單位',
          description: '上午班',
          status: 'confirmed'
        },
        {
          id: 2,
          date: '2025-10-01',
          timeRange: '18:00-21:00',
          location: '北區青年空間北區青年空間北區青年空間北區青年空間',
          description: '晚班',
          status: 'confirmed'
        },

        // 10月2日 - 已完成签到且PT已确认工作时数（灰色）
        {
          id: 3,
          date: '2025-10-02',
          timeRange: '09:00-13:00',
          location: 'ABC單位',
          description: '上午班',
          status: 'confirmed'
        },
        {
          id: 4,
          date: '2025-10-02',
          timeRange: '18:00-21:00',
          location: '北區青年空間',
          description: '晚班',
          status: 'confirmed'
        },

        // 添加一些2022年12月的数据用于列表视图演示
        {
          id: 101,
          date: '2022-12-01',
          timeRange: '12:00-16:00',
          location: 'ABC單位',
          description: '第一節',
          status: 'confirmed'
        },
        {
          id: 102,
          date: '2022-12-01',
          timeRange: '16:00-19:00',
          location: '北區青年空間',
          description: '第二節',
          status: 'confirmed'
        },
        {
          id: 103,
          date: '2022-12-01',
          timeRange: '19:00-22:00',
          location: '北區青年空間',
          description: '第三節',
          status: 'confirmed'
        },
        {
          id: 104,
          date: '2022-12-02',
          timeRange: '12:00-16:00',
          location: 'ABC單位',
          description: '第一節',
          status: 'confirmed'
        },
        {
          id: 105,
          date: '2022-12-02',
          timeRange: '16:00-19:00',
          location: '北區青年空間',
          description: '第二節',
          status: 'confirmed'
        },
        {
          id: 106,
          date: '2022-12-02',
          timeRange: '19:00-22:00',
          location: '北區青年空間',
          description: '第三節',
          status: 'confirmed'
        },
        {
          id: 107,
          date: '2022-12-03',
          timeRange: '12:00-16:00',
          location: 'ABC單位',
          description: '第一節',
          status: 'confirmed'
        },
        {
          id: 108,
          date: '2022-12-03',
          timeRange: '16:00-19:00',
          location: '北區青年空間',
          description: '第二節',
          status: 'confirmed'
        },
        {
          id: 109,
          date: '2022-12-03',
          timeRange: '19:00-22:00',
          location: '北區青年空間',
          description: '第三節',
          status: 'confirmed'
        },
        {
          id: 110,
          date: '2022-12-05',
          timeRange: '12:00-16:00',
          location: 'ABC單位',
          description: '第一節',
          status: 'pending'
        },
        {
          id: 111,
          date: '2022-12-05',
          timeRange: '16:00-19:00',
          location: '北區青年空間',
          description: '第二節',
          status: 'pending'
        },

        // 10月3日 - 已完成签到且PT已确认工作时数（灰色）
        {
          id: 5,
          date: '2025-10-03',
          timeRange: '09:00-13:00',
          location: 'ABC單位',
          description: '上午班',
          status: 'confirmed'
        },
        {
          id: 6,
          date: '2025-10-03',
          timeRange: '18:00-21:00',
          location: '北區青年空間',
          description: '晚班',
          status: 'confirmed'
        },

        // 10月4日 - 已完成签到且PT已确认工作时数（灰色）
        {
          id: 7,
          date: '2025-10-04',
          timeRange: '09:00-13:00',
          location: 'ABC單位',
          description: '上午班',
          status: 'confirmed'
        },
        {
          id: 8,
          date: '2025-10-04',
          timeRange: '18:00-21:00',
          location: '北區青年空間',
          description: '晚班',
          status: 'confirmed'
        },

        // 10月5日
        {
          id: 9,
          date: '2025-10-05',
          timeRange: '09:00-13:00',
          location: 'ABC單位',
          description: '上午班',
          status: 'pending'
        },
        {
          id: 10,
          date: '2025-10-05',
          timeRange: '18:00-21:00',
          location: '北區青年空間',
          description: '晚班',
          status: 'pending'
        },

        // 10月6日
        {
          id: 11,
          date: '2025-10-06',
          timeRange: '09:00-13:00',
          location: 'ABC單位',
          description: '上午班',
          status: 'pending'
        },
        {
          id: 12,
          date: '2025-10-06',
          timeRange: '18:00-21:00',
          location: '北區青年空間',
          description: '晚班',
          status: 'pending'
        },

        // 10月7日
        {
          id: 13,
          date: '2025-10-07',
          timeRange: '09:00-13:00',
          location: 'ABC單位',
          description: '上午班',
          status: 'pending'
        },

        // 10月8日
        {
          id: 14,
          date: '2025-10-08',
          timeRange: '14:00-18:00',
          location: 'DEF單位',
          description: '下午班',
          status: 'pending'
        },

        // 10月9日 - 今天（有未完成记录）
        {
          id: 15,
          date: '2025-10-09',
          timeRange: '09:00-13:00',
          location: 'ABC單位',
          description: '上午班',
          status: 'incomplete'
        },
        {
          id: 16,
          date: '2025-10-09',
          timeRange: '14:00-18:00',
          location: 'ABC單位',
          description: '下午班',
          status: 'incomplete'
        },

        // 10月10日
        {
          id: 17,
          date: '2025-10-10',
          timeRange: '09:00-13:00',
          location: '北區青年空間',
          description: '上午班',
          status: 'pending'
        },
        {
          id: 18,
          date: '2025-10-10',
          timeRange: '18:00-21:00',
          location: '北區青年空間',
          description: '晚班',
          status: 'pending'
        },

        // 10月11日
        {
          id: 19,
          date: '2025-10-11',
          timeRange: '09:00-13:00',
          location: 'GHI單位',
          description: '上午班',
          status: 'pending'
        },

        // 10月12日
        {
          id: 20,
          date: '2025-10-12',
          timeRange: '14:00-18:00',
          location: 'ABC單位',
          description: '下午班',
          status: 'pending'
        },
        {
          id: 21,
          date: '2025-10-12',
          timeRange: '18:00-21:00',
          location: '北區青年空間',
          description: '晚班',
          status: 'pending'
        }


      ],

      // 时间段（用于周视图）
      timeSlots: Array.from({ length: 24 }, (_, i) => i)
    };
  },

  computed: {
    // 当前期间的文本显示
    currentPeriodText() {
      const date = this.selectedDate;
      if (this.viewMode === 'month') {
        return `${date.getFullYear()}年${date.getMonth() + 1}月`;
      } else if (this.viewMode === 'week') {
        const startOfWeek = this.getStartOfWeek(date);
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        return `${startOfWeek.getMonth() + 1}月${startOfWeek.getDate()}日 - ${endOfWeek.getMonth() + 1}月${endOfWeek.getDate()}日`;
      } else {
        // 列表视图显示2022年
        return '2022年';
      }
    },

    // 今日提醒
    todayReminder() {
      const today = '2025-10-09'; // 模拟今天是10月9日
      const todaySchedules = this.schedules.filter(s => s.date === today);
      const incompleteCount = todaySchedules.filter(s => s.status === 'incomplete').length;

      if (incompleteCount > 0) {
        return `今天有${incompleteCount}個未完成的簽到記錄，請及時處理`;
      }
      return null;
    },

    // 分组的排班数据（用于列表视图）
    groupedSchedules() {
      const groups = {};
      // 在列表视图中只显示2022年12月的数据
      const filteredSchedules = this.viewMode === 'list'
        ? this.schedules.filter(schedule => schedule.date.startsWith('2022-12'))
        : this.schedules;

      filteredSchedules.forEach(schedule => {
        if (!groups[schedule.date]) {
          groups[schedule.date] = {
            date: schedule.date,
            schedules: []
          };
        }
        groups[schedule.date].schedules.push(schedule);
      });

      return Object.values(groups).sort((a, b) => new Date(a.date) - new Date(b.date));
    },

    // 周视图的天数据
    weekDays() {
      const startOfWeek = this.getStartOfWeek(this.selectedDate);
      const days = [];
      const today = '2025-10-09'; // 模拟今天是10月9日

      for (let i = 0; i < 7; i++) {
        const date = new Date(startOfWeek);
        date.setDate(startOfWeek.getDate() + i);
        const dateStr = date.toISOString().split('T')[0];

        days.push({
          date: dateStr,
          name: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date.getDay()],
          isToday: dateStr === today
        });
      }

      return days;
    }
  },

  methods: {
    // 切换到上一个时间段
    previousPeriod() {
      const date = new Date(this.selectedDate);
      if (this.viewMode === 'month') {
        date.setMonth(date.getMonth() - 1);
      } else if (this.viewMode === 'week') {
        date.setDate(date.getDate() - 7);
      }
      this.selectedDate = date;
    },

    // 切换到下一个时间段
    nextPeriod() {
      const date = new Date(this.selectedDate);
      if (this.viewMode === 'month') {
        date.setMonth(date.getMonth() + 1);
      } else if (this.viewMode === 'week') {
        date.setDate(date.getDate() + 7);
      }
      this.selectedDate = date;
    },

    // 获取某个日期的排班
    getSchedulesForDate(date) {
      const dateStr = date.toISOString().split('T')[0];
      return this.schedules.filter(schedule => schedule.date === dateStr);
    },

    // 获取某个日期时间的排班（周视图用）
    getSchedulesForDateTime(dateStr, hour) {
      const daySchedules = this.schedules.filter(schedule => schedule.date === dateStr);
      return daySchedules.filter(schedule => {
        const [startTime] = schedule.timeRange.split('-');
        const startHour = parseInt(startTime.split(':')[0]);
        return startHour === hour;
      });
    },

    // 获取日期的CSS类
    getDayClass(date, data) {
      const dateStr = date.toISOString().split('T')[0];
      const schedules = this.getSchedulesForDate(date);
      const today = '2025-10-09'; // 模拟今天是10月9日

      let classes = [];

      if (dateStr === today) {
        classes.push('today');
      }

      if (schedules.length > 0) {
        classes.push('has-schedule');

        const hasIncomplete = schedules.some(s => s.status === 'incomplete');
        const hasConfirmed = schedules.some(s => s.status === 'confirmed');
        const hasPending = schedules.some(s => s.status === 'pending');

        if (hasIncomplete) classes.push('has-incomplete');
        if (hasConfirmed) classes.push('has-confirmed');
        if (hasPending) classes.push('has-pending');
      }

      return classes.join(' ');
    },

    // 格式化日期显示
    formatDate(dateStr) {
      const date = new Date(dateStr);
      const today = '2025-10-09'; // 模拟今天是10月9日
      const tomorrow = '2025-10-10'; // 模拟明天是10月10日

      if (dateStr === today) {
        return '今天';
      } else if (dateStr === tomorrow) {
        return '明天';
      } else {
        return `${date.getMonth() + 1}月${date.getDate()}日`;
      }
    },

    // 判断是否为今天
    isToday(dateStr) {
      return dateStr === '2025-10-09'; // 模拟今天是10月9日
    },

    // 获取周的开始日期（周日）
    getStartOfWeek(date) {
      const d = new Date(date);
      const day = d.getDay();
      const diff = d.getDate() - day;
      return new Date(d.setDate(diff));
    },

    // 跳转到签到/签走页面
    goToAttendance() {
      this.$router.push({
        name: 'WorkflowAttendance',
        query: {
          date: '2025-09-25' // 可以传递选中的日期
        }
      });
    },

    // 格式化日期显示（列表视图专用）
    formatDateForList(dateStr) {
      const date = new Date(dateStr);
      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
    },

    // 获取星期几
    getWeekday(dateStr) {
      const date = new Date(dateStr);
      const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
      return weekdays[date.getDay()];
    },

    // 获取参与人数（根据时间段计算小时数）
    getParticipantCount(schedule) {
      const timeRange = schedule.timeRange;
      const [startTime, endTime] = timeRange.split('-');
      const start = parseInt(startTime.split(':')[0]);
      const end = parseInt(endTime.split(':')[0]);
      return end - start; // 返回小时数
    }
  },

  mounted() {
    // 页面加载时跳转到今天的未完成记录
    this.$nextTick(() => {
      const today = '2025-10-09'; // 模拟今天是10月9日
      const todayIncomplete = this.schedules.find(s =>
        s.date === today && s.status === 'incomplete'
      );

      if (todayIncomplete) {
        // 这里可以添加滚动到对应位置的逻辑
        console.log('跳转到未完成的记录:', todayIncomplete);
      }
    });
  }
};
</script>

<style>
/* 主题样式 */
.schedule-container {
  min-height: 100vh;
  padding: 20px;
  transition: background-color 0.3s ease;
}

.pt-theme {
  background-color: #fff3e0; /* 淺橙色背景 */
}

.unit-theme {
  background-color: #ffffff; /* 白色背景 */
}

/* 头部区域 */
.header-section {
  margin-bottom: 20px;
}

.title-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.title-area h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
}

.check-in-btn {
  font-weight: bold;
}

.custom-gray-tag {
  background-color: #a9a9a9 !important; /* 深灰色 (DarkGray) */
  color: #000000 !important;            /* 黑色文字 */
  border: none !important;              /* 去掉边框（可选） */
  font-weight: bold !important;         /* 加粗字体 */
}

.custom-yellow-tag {
  background-color: #fbc02d !important;
  color: #000 !important;
  border: 1px solid #fdd835 !important;
  transition: 0.3s;
  font-weight: bold !important;         /* 加粗字体 */
}

.custom-green-tag {
  background-color: #388e3c !important;
  color: #000000 !important;            /* 黑色文字 */
  border: none !important;              /* 去掉边框（可选） */
  font-weight: bold !important;         /* 加粗字体 */
}


.status-tags {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

/* 导航区域 */
.navigation-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.date-navigation {
  display: flex;
  align-items: center;
  gap: 15px;
}

.current-period {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  min-width: 150px;
  text-align: center;
}

/* 日历区域 */
.calendar-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

/* 月视图样式 */
.month-view {
  padding: 20px;
}

.calendar-day {
  min-height: 140px !important;
  height: auto !important;
  padding: 8px !important;
  position: relative;
  background: white;
  border-radius: 4px;
  transition: all 0.3s ease;
  display: flex !important;
  flex-direction: column !important;
  overflow: visible !important;
}

.day-number {
  font-weight: bold;
  margin-bottom: 5px;
  position: relative;
}

.today-label {
  background-color: #ff4444;
  color: white;
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 8px;
  margin-left: 5px;
  font-weight: normal;
}

.day-content {
  font-size: 12px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 3px;
  overflow: visible !important;
  min-height: 80px;
}

.schedule-item {
  margin-bottom: 2px;
  padding: 3px 5px;
  border-radius: 3px;
  font-size: 10px;
  line-height: 1.3;
  word-wrap: break-word;
  word-break: break-all;
  overflow-wrap: break-word;
  flex-shrink: 0;
  min-height: 18px;
  display: block;
  color: white !important;
}

.schedule-text {
  color: white !important;
  font-weight: normal !important;
  word-wrap: break-word;
  word-break: break-all;
  overflow-wrap: break-word;
  white-space: normal;
}

/* 确保所有状态下的文字都是白色 */
.calendar-day .schedule-item .schedule-text,
.schedule-item.confirmed .schedule-text,
.schedule-item.incomplete .schedule-text,
.schedule-item.pending .schedule-text {
  color: #ffffff !important;
}

/* 已完成签到且PT已确认工作时数 - 灰色 */
.schedule-item.confirmed {
  background-color: #a9a9a9;
  color: #ffffff !important;
  border: 1px solid #ced4da;
}

/* 可供签到但未完成签到记录 - 黄色 */
.schedule-item.incomplete {
  background-color: #fbc02d;
  color: #ffffff !important;
  border: 1px solid #ffeaa7;
}

/* 未开放签到，只可查看工作内容 - 深绿色 */
.schedule-item.pending {
  background-color: #388e3c;
  color: #ffffff !important;
  border: 1px solid #c3e6cb;
}

.calendar-day.today {
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

/* 列表视图样式 */
.list-view {
  padding: 20px;
  max-height: 600px;
  overflow-y: auto;
}

.date-group {
  margin-bottom: 20px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.date-header {
  background: #f0f0f0;
  padding: 12px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
}

.date-text {
  font-size: 14px;
  font-weight: bold;
  color: #333;
}

.weekday-text {
  font-size: 14px;
  color: #666;
}

.schedules-list {
  display: flex;
  flex-direction: column;
}

.schedule-row {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #f5f5f5;
  background: white;
}

.schedule-row:last-child {
  border-bottom: none;
}

.time-range {
  font-size: 14px;
  color: #333;
  min-width: 100px;
  font-weight: normal;
}

.schedule-details {
  flex: 1;
  margin-left: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.schedule-label {
  font-size: 14px;
  color: #333;
  min-width: 60px;
}

.time-display {
  font-size: 14px;
  color: #333;
}

.participant-count {
  font-size: 14px;
  color: #666;
}


/* 周视图样式 */
.week-view {
  padding: 20px;
}

.week-header {
  display: flex;
  border-bottom: 2px solid #e0e0e0;
  margin-bottom: 10px;
}

.time-column {
  width: 80px;
  flex-shrink: 0;
}

.day-column {
  flex: 1;
  text-align: center;
  padding: 10px;
  border-right: 1px solid #e0e0e0;
}

.day-column:last-child {
  border-right: none;
}

.day-name {
  font-weight: bold;
  color: #666;
  margin-bottom: 5px;
}

.day-date {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.day-date.today {
  color: #2196f3;
  background: #e3f2fd;
  border-radius: 50%;
  height: 30px;
  line-height: 30px;
  margin: 0 auto;
}

.week-content {
  max-height: 500px;
  overflow-y: auto;
}

.time-slots {
  display: flex;
  flex-direction: column;
}

.time-slot {
  display: flex;
  min-height: 60px;
  border-bottom: 1px solid #f0f0f0;
}

.time-label {
  width: 80px;
  flex-shrink: 0;
  padding: 10px;
  font-size: 12px;
  color: #666;
  text-align: right;
  border-right: 1px solid #e0e0e0;
}

.hour-row {
  display: flex;
  flex: 1;
}

.day-cell {
  flex: 1;
  padding: 5px;
  border-right: 1px solid #f0f0f0;
  position: relative;
}

.day-cell:last-child {
  border-right: none;
}

.schedule-block {
  padding: 4px 6px;
  border-radius: 4px;
  font-size: 11px;
  margin-bottom: 2px;
  cursor: pointer;
}

.schedule-block.completed {
  background-color: #d4edda;
  color: #155724;
}

.schedule-block.incomplete {
  background-color: #fff3cd;
  color: #856404;
}

.schedule-block.pending {
  background-color: #d1ecf1;
  color: #0c5460;
}

.schedule-title {
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Element UI 日历自定义样式 */
.el-calendar {
  background: transparent;
}

.el-calendar__header {
  display: none !important; /* 隐藏默认头部，使用自定义导航 */
}

/* 隐藏Element UI默认的月份导航 */
.el-calendar__button-group {
  display: none !important;
}

.el-calendar__body {
  padding: 0;
}

.el-calendar-table {
  border: none;
}

.el-calendar-table thead th {
  background: #f5f5f5;
  color: #333;
  font-weight: bold;
  padding: 15px 0;
  border: 1px solid #e0e0e0;
}

/* 自定义周几显示 */
.el-calendar-table thead th:nth-child(1)::after { content: "周日"; }
.el-calendar-table thead th:nth-child(2)::after { content: "周一"; }
.el-calendar-table thead th:nth-child(3)::after { content: "周二"; }
.el-calendar-table thead th:nth-child(4)::after { content: "周三"; }
.el-calendar-table thead th:nth-child(5)::after { content: "周四"; }
.el-calendar-table thead th:nth-child(6)::after { content: "周五"; }
.el-calendar-table thead th:nth-child(7)::after { content: "周六"; }

.el-calendar-table thead th {
  font-size: 0; /* 隐藏原始文本 */
}

.el-calendar-table thead th::after {
  font-size: 14px; /* 恢复字体大小 */
  font-weight: bold;
}

.el-calendar-table td {
  border: 1px solid #e0e0e0;
  vertical-align: top;
  padding: 0 !important;
  height: auto !important;
  min-height: 140px !important;
}

.el-calendar-table .el-calendar-day {
  padding: 0 !important;
  height: auto !important;
  min-height: 140px !important;
  display: flex !important;
  flex-direction: column !important;
}

/* 强制Element UI日历单元格自适应高度 */
.el-calendar__body {
  padding: 0 !important;
}

.el-calendar-table tbody tr {
  height: auto !important;
}

.el-calendar-table tbody td {
  height: auto !important;
  min-height: 140px !important;
  vertical-align: top !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .navigation-section {
    flex-direction: column;
    gap: 15px;
  }

  .schedule-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .schedule-time {
    min-width: auto;
  }

  .schedule-info {
    margin-left: 0;
  }

  .schedule-actions {
    margin-left: 0;
    align-self: flex-end;
  }

  .week-view {
    display: none; /* 在移动端隐藏周视图 */
  }

  .view-switcher .el-radio-button:last-child {
    display: none; /* 在移动端隐藏周视图选项 */
  }

  /* 移动端日历单元格适配 */
  .calendar-day {
    min-height: 100px !important;
    padding: 4px !important;
  }

  .schedule-item {
    font-size: 9px;
    padding: 2px 3px;
    margin-bottom: 1px;
    min-height: 16px;
  }

  .schedule-text {
    line-height: 1.2;
  }

  .day-number {
    font-size: 12px;
    margin-bottom: 3px;
  }

  .today-label {
    font-size: 8px;
    padding: 1px 3px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
  .calendar-day {
    min-height: 80px !important;
    padding: 2px !important;
  }

  .schedule-item {
    font-size: 8px;
    padding: 1px 2px;
    min-height: 14px;
  }

  .day-number {
    font-size: 10px;
    margin-bottom: 2px;
  }

  .schedule-text {
    line-height: 1.1;
  }
}
</style>