# 我的輪值表組件使用說明

## 功能特點

1. **用戶主題區分**
   - PT同事：淺橙色背景 (#fff3e0)
   - Unit同事：白色背景 (#ffffff)

2. **多視圖模式**
   - 月視圖：使用 Element UI 的 el-calendar 組件
   - 列表視圖：按日期分組顯示排班詳情
   - 周視圖：以周日到周六為一周的時間表格式

3. **智能提醒**
   - 自動跳轉到今日排班詳情
   - 高亮顯示未完成的簽到記錄
   - 提供狀態標籤說明

4. **狀態管理**
   - 已完成簽到記錄（綠色）
   - 未完成簽到記錄（黃色）
   - 未開始的工作時段（藍色）

## 使用方法

### 1. 在路由中註冊

```javascript
// router/index.js
{
  path: '/workflow/mycalendar',
  name: 'MyCalendar',
  component: () => import('@/views/workflow/mycalendar.vue'),
  meta: {
    title: '我的輪值表',
    requiresAuth: true
  }
}
```

### 2. 組件配置

```javascript
// 在組件的 data 中設置用戶類型
data() {
  return {
    isPTUser: true, // 從用戶信息中獲取，true為PT同事，false為Unit同事
    // ... 其他配置
  }
}
```

### 3. 數據格式

排班數據格式：
```javascript
{
  id: 1,
  date: '2025-01-09', // YYYY-MM-DD 格式
  timeRange: '09:00-13:00',
  location: 'ABC單位',
  description: '上午班',
  status: 'completed' // completed, incomplete, pending
}
```

## 響應式設計

- 桌面端：支持所有三種視圖模式
- 移動端：自動隱藏周視圖，優化列表和月視圖顯示

## 自定義樣式

可以通過修改 CSS 變量來自定義主題色彩：

```css
.pt-theme {
  background-color: #fff3e0; /* 可自定義PT主題色 */
}

.unit-theme {
  background-color: #ffffff; /* 可自定義Unit主題色 */
}
```
