<template>
  <div class="card-preview-wrapper">
    <!-- 图片展示区域 -->
    <div class="card-image-container" @click="openPreview">
      <img
        :src="displayImage"
        alt="名片图片"
        class="card-image"
        :style="imageStyle"
      />
      <div  class="back-indicator">
        <i class="el-icon-view"></i> {{ $t("cardPreview.zoomIn") }}
      </div>
    </div>

    <!-- 预览对话框 -->
    <el-dialog
      :visible.sync="previewVisible"
      fullscreen
      :show-close="false"
      custom-class="card-preview-dialog"
      @close="handleClose"
    >
      <div
        class="preview-backdrop"
        :style="{ backgroundColor: `rgba(0, 0, 0, ${backdropOpacity})` }"
        @click="closePreview"
      ></div>

      <div class="preview-content">
        <!-- 单面展示模式 -->
        <div v-if="!hasBackSide" class="single-mode">
          <img
            :src="frontImage"
            :alt="$t('cardPreview.front')"
            class="preview-image"
            :style="getTransformStyle('front')"
          />
        </div>

        <!-- 双面展示模式 -->
        <div
          v-else
          class="dual-mode"
          :style="{ flexDirection: isFlipped ? 'row-reverse' : 'row' }"
        >
          <div 
            class="card-side"
            :class="{ 'active-side': activeSide === 'front' }"
            @click.stop="setActiveSide('front')"
            :style="{ zIndex: activeSide === 'front' ? 10 : 1 }"
          >
            <img
              :src="frontImage"
              :alt="$t('cardPreview.front')"
              class="preview-image"
              :style="getTransformStyle('front')"
            />
            <div class="side-label">{{ $t("cardPreview.front") }}</div>
            <div v-if="activeSide === 'front'" class="active-indicator"></div>
          </div>

          <div 
            class="card-side"
            :class="{ 'active-side': activeSide === 'back' }"
            @click.stop="setActiveSide('back')"
            :style="{ zIndex: activeSide === 'back' ? 10 : 1 }"
          >
            <img
              :src="backImage"
              :alt="$t('cardPreview.back')"
              class="preview-image"
              :style="getTransformStyle('back')"
            />
            <div class="side-label">{{ $t("cardPreview.back") }}</div>
            <div v-if="activeSide === 'back'" class="active-indicator"></div>
          </div>
        </div>
      </div>

      <!-- 操作按钮区域 -->
      <div class="preview-actions">
        <!-- 正反调转按钮（仅双面时显示） -->
        <el-tooltip
          v-if="hasBackSide"
          :content="$t('cardPreview.flip')"
          placement="top"
        >
          <el-button
            type="text"
            @click.stop="flipSides"
            class="flip-sides-button"
          >
            <i class="el-icon-sort"></i>
          </el-button>
        </el-tooltip>

        <!-- 缩放控制 -->
        <div class="control-group">
          <el-tooltip :content="$t('cardPreview.zoomOut')" placement="top">
            <el-button
              type="text"
              @click.stop="zoomOut"
              :disabled="getCurrentTransform().scale <= 0.5"
            >
              <i class="el-icon-zoom-out"></i>
            </el-button>
          </el-tooltip>
          <el-slider
            v-model="getCurrentTransform().scale"
            :min="0.5"
            :max="2"
            :step="0.1"
            :format-tooltip="(val) => `${Math.round(val * 100)}%`"
            style="width: 120px; margin: 0 10px"
          ></el-slider>
          <el-tooltip :content="$t('cardPreview.zoomIn')" placement="top">
            <el-button 
              type="text" 
              @click.stop="zoomIn" 
              :disabled="getCurrentTransform().scale >= 2"
            >
              <i class="el-icon-zoom-in"></i>
            </el-button>
          </el-tooltip>
        </div>

        <!-- 旋转控制 -->
        <div class="control-group">
          <el-tooltip :content="$t('cardPreview.rotateLeft')" placement="top">
            <el-button type="text" @click.stop="rotateLeft">
              <i class="el-icon-refresh-left"></i>
            </el-button>
          </el-tooltip>
          <span class="rotation-value">{{ getCurrentTransform().rotation }}°</span>
          <el-tooltip :content="$t('cardPreview.rotateRight')" placement="top">
            <el-button type="text" @click.stop="rotateRight">
              <i class="el-icon-refresh-right"></i>
            </el-button>
          </el-tooltip>
        </div>

        <!-- 重置 -->
        <el-tooltip :content="$t('cardPreview.reset')" placement="top">
          <el-button
            type="text"
            @click.stop="resetTransform"
            class="reset-button"
          >
            <i class="el-icon-refresh"></i>
          </el-button>
        </el-tooltip>

        <!-- 关闭 -->
        <el-tooltip :content="$t('cardPreview.close')" placement="top">
          <el-button
            type="text"
            @click.stop="closePreview"
            class="close-button"
          >
            <i class="el-icon-close"></i>
          </el-button>
        </el-tooltip>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "CardFlipPreview",
  props: {
    cardData: {
      type: Object,
      required: true,
      validator: (value) => {
        return value.imageUrl !== undefined;
      },
    },
    imageStyle: {
      type: Object,
      default: () => ({
        width: "200px",
        height: "120px",
        "object-fit": "cover",
      }),
    },
    backdropOpacity: {
      type: Number,
      default: 0.7,
      validator: (value) => {
        return value >= 0 && value <= 1;
      },
    },
  },
  data() {
    return {
      previewVisible: false,
      isFlipped: false,
      activeSide: "front", // 当前激活的面
      frontTransform: { scale: 1, rotation: 0 }, // 正面变换参数
      backTransform: { scale: 1, rotation: 0 },  // 反面变换参数
    };
  },
  computed: {
    displayImage() {
      return this.cardData.imageUrl === '*' 
        ? require("@/assets/pcm/card-detail/access_denied.jpg")
        : this.cardData.imageUrl;
    },
    frontImage() {
      return this.cardData.imageUrl === '*' 
        ? require("@/assets/pcm/card-detail/access_denied.jpg")
        : this.cardData.imageUrl;
    },
    backImage() {
      return this.cardData.backUrl === '*' 
        ? require("@/assets/pcm/card-detail/access_denied.jpg")
        : this.cardData.backUrl;
    },
    hasBackSide() {
      return !!this.cardData.backUrl;
    },
  },
  methods: {
    // 获取当前激活面的变换参数
    getCurrentTransform() {
      return this.activeSide === "front" 
        ? this.frontTransform 
        : this.backTransform;
    },
    
    // 获取变换样式
    getTransformStyle(side) {
      const transform = side === "front" 
        ? this.frontTransform 
        : this.backTransform;
      return {
        transform: `scale(${transform.scale}) rotate(${transform.rotation}deg)`,
        cursor: transform.scale > 1 ? "grab" : "default",
      };
    },
    
    // 设置当前激活的面
    setActiveSide(side) {
      this.activeSide = side;
    },
    
    openPreview() {
      this.previewVisible = true;
      this.resetTransform();
    },
    
    closePreview() {
      this.previewVisible = false;
    },
    
    handleClose() {
      this.closePreview();
    },
    
    flipSides() {
      this.isFlipped = !this.isFlipped;
    },
    
    zoomIn() {
      const transform = this.getCurrentTransform();
      if (transform.scale < 2) {
        transform.scale = Math.min(transform.scale + 0.1, 2);
      }
    },
    
    zoomOut() {
      const transform = this.getCurrentTransform();
      if (transform.scale > 0.5) {
        transform.scale = Math.max(transform.scale - 0.1, 0.5);
      }
    },
    
    rotateLeft() {
      const transform = this.getCurrentTransform();
      transform.rotation -= 90;
      if (transform.rotation <= -360) transform.rotation = 0;
    },
    
    rotateRight() {
      const transform = this.getCurrentTransform();
      transform.rotation += 90;
      if (transform.rotation >= 360) transform.rotation = 0;
    },
    
    resetTransform() {
      this.frontTransform = { scale: 1, rotation: 0 };
      this.backTransform = { scale: 1, rotation: 0 };
      this.isFlipped = false;
      this.activeSide = "front";
    },
  },
};
</script>

<style lang="scss" scoped>
.card-preview-wrapper {
  display: inline-block;

  .card-image-container {
    cursor: pointer;
    transition: transform 0.3s;
    position: relative;

    &:hover {
      transform: scale(1.02);
      .back-indicator {
        opacity: 1;
      }
    }

    .card-image {
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      display: block;
    }

    .back-indicator {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba(0, 0, 0, 0.6);
      color: white;
      text-align: center;
      padding: 4px;
      font-size: 12px;
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;
      opacity: 0;
      transition: opacity 0.3s;

      i {
        margin-right: 4px;
      }
    }
  }
}

// 预览对话框样式
::v-deep .card-preview-dialog {
  background-color: transparent;
  box-shadow: none;

  .el-dialog__header {
    padding: 0;
    border: none;
  }

  .el-dialog__body {
    padding: 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: transparent;
    overflow: hidden;
  }
}

.preview-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  transition: background-color 0.3s;
}

.preview-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  overflow: auto;

  .single-mode {
    display: flex;
    justify-content: center;
    align-items: center;

    .preview-image {
      max-width: 90vw;
      max-height: 90vh;
      object-fit: contain;
      transition: transform 0.3s ease;
      transform-origin: center center;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      border-radius: 8px;
    }
  }

  .dual-mode {
    display: flex;
    gap: 40px;
    align-items: center;
    justify-content: center;
    transition: flex-direction 0.3s ease;
    position: relative;

    .card-side {
      position: relative;
      background: white;
      padding: 15px;
      border-radius: 8px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      transition: all 0.3s ease;
      cursor: pointer;
      
      &:hover {
        transform: translateY(-5px);
      }
      
      &.active-side {
        box-shadow: 0 0 0 3px #409eff, 0 10px 30px rgba(0, 0, 0, 0.2);
      }

      .preview-image {
        max-width: 40vw;
        max-height: 80vh;
        object-fit: contain;
        transition: transform 0.3s ease;
        transform-origin: center center;
      }

      .side-label {
        position: absolute;
        bottom: -25px;
        left: 50%;
        transform: translateX(-50%);
        color: white;
        font-size: 16px;
        font-weight: bold;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
      }

      .active-indicator {
        position: absolute;
        top: 5px;
        right: 5px;
        width: 10px;
        height: 10px;
        background-color: #409eff;
        border-radius: 50%;
      }
    }
  }
}

.preview-actions {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 15px;
  background: rgba(255, 255, 255, 0.9);
  padding: 10px 20px;
  border-radius: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

  .control-group {
    display: flex;
    align-items: center;
    gap: 10px;

    .rotation-value {
      min-width: 40px;
      text-align: center;
      font-size: 14px;
      color: #666;
    }
  }

  .el-button {
    font-size: 18px;
    color: #606266;
    padding: 8px;

    &:hover {
      color: #409eff;
    }

    &.flip-sides-button {
      margin-right: 10px;
    }

    &.reset-button {
      margin-left: 10px;
    }

    &.close-button {
      color: #f56c6c;
    }
  }

  .el-slider {
    ::v-deep .el-slider__button {
      width: 14px;
      height: 14px;
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .preview-content {
    .dual-mode {
      flex-direction: column !important;
      gap: 60px;

      .card-side {
        .preview-image {
          max-width: 80vw;
        }
      }
    }
  }

  .preview-actions {
    bottom: 15px;
    width: 90%;
    padding: 8px 15px;

    .control-group {
      flex: 1;
      justify-content: center;
    }

    .flip-sides-button {
      display: none;
    }

    .reset-button,
    .close-button {
      display: none;
    }
  }
}
</style>