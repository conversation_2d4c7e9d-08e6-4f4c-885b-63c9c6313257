import request from '@/utils/request'
import {
  getToken
} from '@/utils/auth'

// 删除文件
export function deleteFile(id) {
  return request({
    url: '/infra/file/delete?id=' + id,
    method: 'delete'
  })
}

// 获得文件分页
export function getFilePage(query) {
  return request({
    url: '/infra/file/page',
    method: 'get',
    params: query
  })
}


/**
 * 文件上传
 * @param {*} data 
 * @returns 
 */
export function fileUpload(data) {
  data.token = getToken();
  return request({
    url: '/infra/file/upload',
    method: 'post',
    data: data
  })
}
