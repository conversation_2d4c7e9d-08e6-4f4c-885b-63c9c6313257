<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form 
      :model="queryParams" 
      ref="queryForm" 
      size="small" 
      :inline="true" 
      v-show="showSearch" 
      label-width="100px"
      class="search-form"
    >
      <el-form-item :label="$t('approvalId')" prop="id">
        <el-input 
          v-model="queryParams.id" 
          :placeholder="$t('enterApprovalId')" 
          clearable 
          @change="handleQuery"
        />
      </el-form-item>
      
      <el-form-item :label="$t('applicant')" prop="applicant">
        <el-input 
          v-model="queryParams.applicant" 
          :placeholder="$t('enterApplicant')" 
          clearable 
          @change="handleQuery"
        />
      </el-form-item>
      
      <el-form-item :label="$t('applyTime')" prop="createTime">
        <el-date-picker 
          v-model="queryParams.createTime" 
          style="width: 240px" 
          value-format="yyyy-MM-dd HH:mm:ss" 
          type="daterange"
          range-separator="-" 
          :start-placeholder="$t('startTime')" 
          :end-placeholder="$t('endTime')" 
          :default-time="['00:00:00', '23:59:59']"
          @change="handleQuery"
        />
      </el-form-item>
      
      <el-form-item :label="$t('approvalStatus')" prop="status">
        <el-select 
          v-model="queryParams.status" 
          :placeholder="$t('selectApprovalStatus')" 
          clearable
          @change="handleQuery"
        >
          <el-option 
            v-for="dict in getDictDatas(DICT_TYPE.EXPORT_APPROVAL_STATUS)"
            :key="dict.value" 
            :label="dict.label" 
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item :label="$t('exportStatus')" prop="exportStatus">
        <el-select 
          v-model="queryParams.exportStatus" 
          :placeholder="$t('selectExportStatus')" 
          clearable
          @change="handleQuery"
        >
          <el-option 
            v-for="dict in getDictDatas(DICT_TYPE.EXPORT_STATUS)"
            :key="dict.value" 
            :label="dict.label" 
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item class="search-buttons">
        <el-button type="primary" @click="handleQuery">
          {{$t('search')}}
        </el-button>
        <el-button @click="resetQuery">
          {{$t('reset')}}
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar 
        :showSearch.sync="showSearch" 
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- Tab切换 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabChange">
      <el-tab-pane :label="$t('pendingApproval')" name="approval">
        <!-- 待审批表格 -->
        <el-table 
          v-loading="loading" 
          :data="approvalList" 
          :empty-text="$t('noData')"
          @sort-change="handleSortChange"
          class="table-container"
        >
         <el-table-column 
            :label="$t('approvalId')" 
            align="center" 
            prop="id"
            min-width="160"
          />
          <el-table-column 
            :label="$t('applicant')" 
            align="center" 
            prop="requesterName"
            min-width="120"
          />
          <el-table-column 
            :label="$t('exportCount')" 
            align="center" 
            prop="exportCount"
            sortable
            min-width="100"
          >
            <template #default="{row}">
              {{ row.exportCount }} {{ $t('items') }}
            </template>
          </el-table-column>
          <el-table-column 
            :label="$t('exportRemark')" 
            align="center" 
            prop="requesterComment"
            min-width="180"
            show-overflow-tooltip
          />
          <el-table-column 
            :label="$t('applyTime')" 
            align="center" 
            prop="applyTime"
            sortable
            min-width="160"
          >
            <template #default="{row}">
              {{ parseTime(row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column 
            :label="$t('approvalRemark')" 
            align="center" 
            prop="approverComment"
            min-width="180"
            show-overflow-tooltip
          />
          <el-table-column 
            :label="$t('approvalStatus')" 
            align="center" 
            prop="status"
            min-width="120"
          >
            <template #default="{row}">
              <dict-tag :type="DICT_TYPE.EXPORT_APPROVAL_STATUS" :value="row.status" />
            </template>
          </el-table-column>
          <el-table-column 
            :label="$t('action')" 
            align="center" 
            class-name="small-padding fixed-width"
            min-width="120"
            fixed="right"
          >
            <template #default="{row}">
              <el-button 
                size="mini" 
                type="text"
                @click="handleApproval(row)"
                :disabled="row.status !== 'PENDING'"
              >
                {{$t('approve')}}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <pagination 
          v-show="approvalTotal > 0" 
          :total="approvalTotal" 
          :page.sync="queryParams.pageNo" 
          :limit.sync="queryParams.pageSize"
          @pagination="getApprovalList"
        />
      </el-tab-pane>
      
      <el-tab-pane :label="$t('pendingExport')" name="export">
        <!-- 待导出表格 -->
        <el-table 
          v-loading="loading" 
          :data="exportList" 
          :empty-text="$t('noData')"
          @sort-change="handleSortChange"
          class="table-container"
        >
          <el-table-column 
            :label="$t('approvalId')" 
            align="center" 
            prop="id"
            min-width="160"
          />
          <el-table-column 
            :label="$t('applicant')" 
            align="center" 
            prop="requesterName"
            min-width="120"
          />
          <el-table-column 
            :label="$t('exportCount')" 
            align="center" 
            prop="exportCount"
            sortable
            min-width="100"
          >
            <template #default="{row}">
              {{ row.exportCount }} {{ $t('items') }}
            </template>
          </el-table-column>
          <el-table-column 
            :label="$t('exportRemark')" 
            align="center" 
            prop="requesterComment"
            min-width="180"
            show-overflow-tooltip
          />
          <el-table-column 
            :label="$t('applyTime')" 
            align="center" 
            prop="createTime"
            sortable
            min-width="160"
          >
            <template #default="{row}">
              {{ parseTime(row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column 
            :label="$t('approver')" 
            align="center" 
            prop="approverName"
            min-width="120"
          />
          <el-table-column 
            :label="$t('approvalRemark')" 
            align="center" 
            prop="approverComment"
            min-width="180"
            show-overflow-tooltip
          />
          <el-table-column 
            :label="$t('approvalTime')" 
            align="center" 
            prop="approveTime"
            sortable
            min-width="160"
          >
            <template #default="{row}">
              {{ parseTime(row.approveTime) }}
            </template>
          </el-table-column>
          <el-table-column 
            :label="$t('exportStatus')" 
            align="center" 
            prop="exportStatus"
            min-width="120"
          >
            <template #default="{row}">
              <dict-tag :type="DICT_TYPE.EXPORT_STATUS" :value="row.exportStatus" />
            </template>
          </el-table-column>
          <el-table-column 
            :label="$t('action')" 
            align="center" 
            class-name="small-padding fixed-width"
            min-width="120"
            fixed="right"
          >
            <template #default="{row}">
              <el-button 
                size="mini" 
                type="text"
                @click="handleExport(row)"
                :disabled="row.status !== 'APPROVED' || row.exportStatus === 'EXPORTED'"
              >
                {{$t('export')}}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <pagination 
          v-show="exportTotal > 0" 
          :total="exportTotal" 
          :page.sync="queryParams.pageNo" 
          :limit.sync="queryParams.pageSize"
          @pagination="getExportList"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 审批对话框 -->
    <el-dialog 
      :title="$t('approvalDialogTitle')" 
      :visible.sync="approvalDialogVisible" 
      width="600px" 
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form 
        ref="approvalForm" 
        :model="approvalForm" 
        label-width="120px" 
        size="medium"
        :rules="approvalRules"
      >
        <el-form-item :label="$t('applicant') + '：'">
          {{ approvalForm.requesterName }}
        </el-form-item>
        <el-form-item :label="$t('exportCount') + '：'">
          {{ approvalForm.exportCount }} {{ $t('items') }}
        </el-form-item>
        <el-form-item :label="$t('exportRemark') + '：'">
          {{ approvalForm.requesterComment }}
        </el-form-item>
        <el-form-item :label="$t('applyTime') + '：'">
          {{ parseTime(approvalForm.createTime) }}
        </el-form-item>
        <el-form-item :label="$t('approvalRemark')" prop="approverComment">
          <el-input
            v-model="approvalForm.approverComment"
            type="textarea"
            :placeholder="$t('enterApprovalRemark')"
            :autosize="{ minRows: 3, maxRows: 6 }"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="approvalDialogVisible = false">
          {{$t('cancel')}}
        </el-button>
        <el-button type="danger" @click="handleReject">
          {{$t('reject')}}
        </el-button>
        <el-button type="primary" @click="handleApprove">
          {{$t('approve')}}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { approvalList, exportList, approve,downloadExport } from "@/api/pcm/approval";
export default {
  name: "ExportApproval",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 当前激活的tab
      activeTab: 'approval',
      // 待审批列表数据
      approvalList: [],
      approvalTotal: 0,
      // 待导出列表数据
      exportList: [],
      exportTotal: 0,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        id:undefined,
        approvalId: undefined,
        applicant: undefined,
        createTime: [],
        status: undefined,
        exportStatus: undefined,
        orderByColumn: 'applyTime',
        isAsc: 'desc'
      },
      // 审批对话框显示状态
      approvalDialogVisible: false,
      // 审批表单数据
      approvalForm: {
        recordId: '',
        approvalId: '',
        requesterName: '',
        exportCount: 0,
        requesterComment: '',
        createTime: '',
        approverComment: '',
        approved:undefined
      },
      // 审批表单验证规则
      approvalRules: {
        approverComment: [
          { required: true, message: this.$t('approvalRemarkRequired'), trigger: 'blur' }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 获取列表数据 */
    getList() {
      if (this.activeTab === 'approval') {
        this.getApprovalList();
      } else {
        this.getExportList();
      }
    },
    
    /** 获取待审批列表 */
    getApprovalList() {
      this.loading = true;
      approvalList(this.queryParams).then(response => {
        this.approvalList = response.data.list;
        this.approvalTotal = response.data.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    
    /** 获取待导出列表 */
    getExportList() {
      this.loading = true;
      exportList(this.queryParams).then(response => {
        this.exportList = response.data.list;
        this.exportTotal = response.data.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    
    /** Tab切换事件 */
    handleTabChange(tab) {
      this.activeTab = tab.name;
      this.resetQuery();
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.orderByColumn = 'createTime';
      this.queryParams.isAsc = 'desc';
      this.handleQuery();
    },
    
    /** 排序事件 */
    handleSortChange(column) {
      if (column.prop) {
        this.queryParams.orderByColumn = column.prop;
        this.queryParams.isAsc = column.order === 'ascending' ? 'asc' : 'desc';
        this.getList();
      }
    },
    
    /** 打开审批对话框 */
    handleApproval(row) {
      this.approvalForm = {
        recordId: row.id,
        approvalId: row.approvalId,
        requesterName: row.requesterName,
        exportCount: row.exportCount,
        requesterComment: row.requesterComment,
        createTime: row.createTime,
        approverComment: row.approverComment || ''
      };
      this.approvalDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.approvalForm.clearValidate();
      });
    },
    
    /** 审批通过 */
    handleApprove() {
      this.$refs.approvalForm.validate(valid => {
        if (valid) {
          this.approvalForm.approved = true
          this.$modal.confirm(this.$t('confirmApprove')).then(() => {
            return approve(this.approvalForm);
          }).then(response => {
            this.$modal.msgSuccess(this.$t('approveSuccess'));
            this.approvalDialogVisible = false;
            this.getList();
          }).catch(() => {});
        }
      });
    },
    
    /** 审批拒绝 */
    handleReject() {
      this.$refs.approvalForm.validate(valid => {
        if (valid) {
           this.approvalForm.approved = false
          this.$modal.confirm(this.$t('confirmReject')).then(() => {
            return approve(this.approvalForm);
          }).then(response => {
            this.$modal.msgSuccess(this.$t('rejectSuccess'));
            this.approvalDialogVisible = false;
            this.getList();
          }).catch(() => {});
        }
      });
    },
    
    /** 执行导出 */
    handleExport(row) {
      this.$modal.confirm(this.$t('confirmExport', [row.exportCount])).then(() => {
        this.loading = true;
        return downloadExport({recordId:row.id});
      }).then(response => {
        this.$download.excel(response, `名片數據導出.xls`);
        this.$modal.msgSuccess(this.$t('exportSuccess'));
        this.getList();
      }).catch(() => {
        this.loading = false;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  
  .search-form {
    ::v-deep .el-form-item {
      margin-bottom: 12px;
    }
    
    .search-buttons {
      margin-left: 10px;
    }
  }
  
  .table-container {
    margin-top: 20px;
  }
  
  @media screen and (max-width: 768px) {
    .search-form {
      ::v-deep .el-form-item {
        width: 100%;
        margin-right: 0;
        
        .el-form-item__content {
          width: calc(100% - 100px);
        }
      }
      
      .search-buttons {
        width: 100%;
        text-align: right;
        margin-left: 0;
        margin-top: 10px;
      }
    }
  }
}
</style>