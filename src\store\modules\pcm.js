const state = {
  cardIds: [],       // 存储传入的名片ID数组
  currentIndex: -1,  // 当前显示的卡片索引
  isLoading: false   // 加载状态（可选）
}

const mutations = {
  // 设置名片ID数组并重置索引
  SET_CARD_IDS(state, ids) {
    state.cardIds = Array.isArray(ids) ? ids : []
    state.currentIndex = state.cardIds.length > 0 ? 0 : -1
  },
  
  // 直接设置当前索引
  SET_CURRENT_INDEX(state, index) {
    if (index >= 0 && index < state.cardIds.length) {
      state.currentIndex = index
    }
  },
  
  // 递增索引（下一张）
  INCREMENT_INDEX(state) {
    if (state.currentIndex < state.cardIds.length - 1) {
      state.currentIndex++
    }
  },
  
  // 递减索引（上一张）
  DECREMENT_INDEX(state) {
    if (state.currentIndex > 0) {
      state.currentIndex--
    }
  },
  
  // 重置状态
  RESET(state) {
    state.cardIds = []
    state.currentIndex = -1
  }
}

const actions = {
  // 接收传入的ID数组
  setCardIds({ commit }, ids) {
    commit('SET_CARD_IDS', ids)
  },
  
  // 跳转到指定ID
  goToCard({ state, commit }, cardId) {
    const index = state.cardIds.indexOf(cardId)
    if (index !== -1) {
      commit('SET_CURRENT_INDEX', index)
    }
    return index !== -1
  },
  
  // 下一张
  nextCard({ state, commit, dispatch }) {
    commit('INCREMENT_INDEX')
    return state.cardIds[state.currentIndex]
  },
  
  // 上一张
  prevCard({ state, commit, dispatch }) {
    commit('DECREMENT_INDEX')
    return state.cardIds[state.currentIndex]
  }
}

const getters = {
  // 当前卡片ID
  currentCardId: state => {
    return state.currentIndex >= 0 
      ? state.cardIds[state.currentIndex] 
      : null
  },
  
  // 下一张卡片ID
  nextCardId: state => {
    return state.currentIndex < state.cardIds.length - 1
      ? state.cardIds[state.currentIndex + 1]
      : null
  },
  
  // 上一张卡片ID
  prevCardId: state => {
    return state.currentIndex > 0
      ? state.cardIds[state.currentIndex - 1]
      : null
  },
  
  // 是否是第一张
  isFirstCard: state => state.currentIndex === 0,
  
  // 是否是最后一张
  isLastCard: state => state.currentIndex === state.cardIds.length - 1,
  
  // 当前进度 (如 "3/10")
  cardProgress: state => {
    return state.cardIds.length > 0
      ? `${state.currentIndex + 1}/${state.cardIds.length}`
      : '0/0'
  },
  
  // 获取完整ID数组
  allCardIds: state => state.cardIds
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}