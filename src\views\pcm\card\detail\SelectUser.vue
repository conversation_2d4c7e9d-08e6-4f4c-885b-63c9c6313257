<template>
  <base-dialog
    v-model="userDialogVisible"
    :title="$t('selectUserAndFields')"
    width="1100px"
    @confirm="handleConfirm"
  >
    <template v-slot:content>
      <div class="permission-selector">
        <!-- 左侧用户列表 -->
        <div class="user-panel">
          <div class="panel-header">
            <h3>{{ $t("userList") }}</h3>
            <el-input
              v-model="userSearchQuery"
              :placeholder="$t('searchUser')"
              clearable
              prefix-icon="el-icon-search"
              size="small"
            />
          </div>
          <div class="scroll-container">
            <div
              v-for="user in filteredUsers"
              :key="user.id"
              v-show="user.id != cardCreatorUserId"
              class="user-item"
              :class="{ active: selectedUser?.id === user.id }"
            >
              <el-checkbox
                v-model="userSelected[user.id]"
                @click.stop
                @change="toggleUserSelection(user)"
              />
              <div class="user-avatar">
                <img v-if="user.avatar" :src="user.avatar" alt="avatar" />
                <div
                  v-else
                  class="avatar-placeholder"
                  :style="{ backgroundColor: getRandomColor(user.nickname) }"
                >
                  {{ getFirstChar(user.nickname) }}
                </div>
              </div>
              <div class="user-info" @click="selectUser(user)">
                <div class="nickname">{{ user.nickname }}</div>
                <div class="dept-name">{{ user.deptName }}</div>
              </div>
              <div
                class="permission-badge"
                v-if="getUserPermissionCount(user.id)"
              >
                {{
                  $t("permissionCount", {
                    count: getUserPermissionCount(user.id),
                  })
                }}
              </div>
            </div>
          </div>
        </div>

        <!-- 中间字段列表 -->
        <div class="field-panel">
          <div class="panel-header">
            <h3>{{ $t("fieldList") }}</h3>
            <div class="actions">
              <el-button
                size="mini"
                @click="selectAllFields"
                :disabled="!selectedUser || !userSelected[selectedUser.id]"
              >
                {{ $t("selectAll") }}
              </el-button>
              <el-button
                size="mini"
                @click="clearAllFields"
                :disabled="!selectedUser || !userSelected[selectedUser.id]"
              >
                {{ $t("clearAll") }}
              </el-button>
            </div>
          </div>
          <div class="scroll-container">
            <template v-if="selectedUser">
              <el-checkbox-group
                v-model="selectedFields"
                :disabled="!selectedUser || !userSelected[selectedUser.id]"
              >
                <div
                  v-for="category in fieldCategories"
                  :key="category.label"
                  class="field-category"
                >
                  <div class="category-header">
                    <h4>{{ category.label }}</h4>
                    <div class="category-actions">
                      <el-button
                        size="mini"
                        type="text"
                        @click.stop="selectAllInCategory(category)"
                        :disabled="
                          !selectedUser || !userSelected[selectedUser.id]
                        "
                      >
                        {{ $t("selectAll") }}
                      </el-button>
                      <el-button
                        size="mini"
                        type="text"
                        @click.stop="clearAllInCategory(category)"
                        :disabled="
                          !selectedUser || !userSelected[selectedUser.id]
                        "
                      >
                        {{ $t("clearAll") }}
                      </el-button>
                    </div>
                  </div>
                  <div class="category-fields">
                    <div
                      v-for="field in category.fields"
                      :key="field.fieldName"
                      class="field-item"
                    >
                      <el-checkbox :label="field.fieldName">
                        {{ field.comment }}
                      </el-checkbox>
                      <div class="field-description">{{ field.fieldName }}</div>
                    </div>
                  </div>
                </div>
              </el-checkbox-group>
            </template>
            <div v-else class="empty-state">
              <i class="el-icon-user"></i>
              <p>{{ $t("selectUserFirst") }}</p>
            </div>
          </div>
        </div>

        <!-- 右侧权限预览 -->
        <div class="preview-panel">
          <div class="panel-header">
            <h3>{{ $t("permissionPreview") }}</h3>
            <div class="summary">
              {{ $t("selectedUsersCount", { count: selectedUserIds.length }) }}
            </div>
          </div>
          <div class="scroll-container">
            <div
              v-for="user in usersWithPermissions"
              :key="user.id"
              class="preview-item"
            >
              <div class="preview-user">
                <div class="user-avatar">
                  <img v-if="user.avatar" :src="user.avatar" alt="avatar" />
                  <div
                    v-else
                    class="avatar-placeholder"
                    :style="{ backgroundColor: getRandomColor(user.nickname) }"
                  >
                    {{ getFirstChar(user.nickname) }}
                  </div>
                </div>
                <div class="user-info">
                  <div class="nickname">{{ user.nickname }}</div>
                  <div class="dept-name">{{ user.deptName }}</div>
                </div>
                <el-button
                  type="text"
                  icon="el-icon-close"
                  @click="removeUserPermissions(user.id)"
                />
              </div>
              <div class="preview-fields">
                <el-tag
                  v-for="fieldKey in getUserPermissions(user.id)"
                  :key="fieldKey"
                  size="small"
                  closable
                  @close="removeFieldPermission(user.id, fieldKey)"
                >
                  {{ getFieldName(fieldKey) }}
                </el-tag>
                <span
                  v-if="!getUserPermissions(user.id).length"
                  class="empty-tip"
                >
                  {{ $t("noPermissionAssigned") }}
                </span>
              </div>
              <div class="edit-permission">
                <el-switch
                  :value="getUserEditPermission(user.id)"
                  :disabled="!hasAllFields(user.id)"
                  active-text="可编辑"
                  @change="(val) => setUserEditPermission(user.id, val)"
                />
                <span v-if="!hasAllFields(user.id)" class="edit-tip">
                  {{ $t("requireAllFieldsForEdit") }}
                </span>
              </div>
            </div>
            <div v-if="!selectedUserIds.length" class="empty-state">
              <i class="el-icon-info"></i>
              <p>{{ $t("noUserSelected") }}</p>
            </div>
          </div>
        </div>
      </div>
    </template>
  </base-dialog>
</template>

<script>
import BaseDialog from "@/components/PcmDialog";
import { listSimpleUsers } from "@/api/system/user";
import { getCardFields, getCardPermissions } from "@/api/pcm/card";
import { Message } from "element-ui";

export default {
  components: { BaseDialog },
  props: {
    cardId: {
      type: [String, Number],
      default: null,
    },
    cardCreatorUserId: {
      type: [String, Number],
      default: null,
    },
    selectedIds: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      userDialogVisible: false,
      users: [],
      fieldCategories: [],
      selectedUser: null,
      userSearchQuery: "",
      loading: false,
      userSelected: {},
      userPermissions: [],
    };
  },
  computed: {
    filteredUsers() {
      if (!this.userSearchQuery) return this.users;
      return this.users.filter(
        (user) =>
          user.nickname
            .toLowerCase()
            .includes(this.userSearchQuery.toLowerCase()) ||
          (user.deptName &&
            user.deptName
              .toLowerCase()
              .includes(this.userSearchQuery.toLowerCase()))
      );
    },
    selectedFields: {
      get() {
        if (!this.selectedUser) return [];
        const permission = this.userPermissions.find(
          (p) => p.userId === this.selectedUser.id
        );
        return permission ? permission.fieldPermissions : [];
      },
      set(value) {
        if (!this.selectedUser) return;
        this.updateUserPermissions(this.selectedUser.id, value);
      },
    },
    selectedUserIds() {
      return this.userPermissions.map((p) => p.userId);
    },
    usersWithPermissions() {
      return this.users.filter((user) =>
        this.selectedUserIds.includes(user.id)
      );
    },
    totalPermissions() {
      return this.userPermissions.reduce(
        (total, p) => total + p.fieldPermissions.length,
        0
      );
    },
    fields() {
      return this.fieldCategories.reduce((acc, category) => {
        return acc.concat(
          category.fields.map((field) => ({
            key: field.fieldName,
            value: field.comment,
          }))
        );
      }, []);
    },
  },
  methods: {
    async open() {
      await Promise.all([this.fetchFields(), this.fetchUsers()]);

      if (this.cardId) {
        await this.fetchCardPermissions();
      }

      this.userDialogVisible = true;
    },

    async fetchUsers() {
      this.loading = true;
      try {
        const res = await listSimpleUsers();
        this.users = res.data;
      } finally {
        this.loading = false;
      }
    },

    async fetchFields() {
      const res = await getCardFields();
      this.fieldCategories = res.data;
    },

    async fetchCardPermissions() {
      this.loading = true;
      try {
        const res = await getCardPermissions(this.cardId);
        this.userPermissions = res.data.map((permission) => ({
          ...permission,
          isEdit: permission.isEdit || false,
        }));

        this.users.forEach((user) => {
          this.userSelected[user.id] = this.userPermissions.some(
            (p) => p.userId === user.id
          );
        });

        if (this.userPermissions.length > 0) {
          const firstUserId = this.userPermissions[0].userId;
          this.selectedUser = this.users.find(
            (user) => user.id === firstUserId
          );
        }
      } finally {
        this.loading = false;
      }
    },

    selectUser(user) {
      if (this.userSelected[user.id]) {
        this.selectedUser = user;
      }
    },

    toggleUserSelection(user) {
      if (this.userSelected[user.id]) {
        // const defaultFields = this.getAllFieldNames();
        // const availableFields = defaultFields.filter(
        //   (field) => this.fieldCategories.some(category =>
        //     category.fields.some(f => f.fieldName === field)
        //   )
        // );

        // if (!this.userPermissions.some((p) => p.userId === user.id)) {
        //   this.userPermissions.push({
        //     userId: user.id,
        //     fieldPermissions: [...availableFields],
        //     isEdit: false
        //   });
        // }
        const defaultFields = ["firstName", "lastName", "imageUrl"].filter(
          (field) =>
            this.fieldCategories.some((category) =>
              category.fields.some((f) => f.fieldName === field)
            )
        );

        if (!this.userPermissions.some((p) => p.userId === user.id)) {
          this.userPermissions.push({
            userId: user.id,
            fieldPermissions: [...defaultFields], // 只使用默认字段
            isEdit: false,
          });
        }

        this.selectedUser = user;
      } else {
        this.userPermissions = this.userPermissions.filter(
          (p) => p.userId !== user.id
        );
        if (this.selectedUser?.id === user.id) {
          this.selectedUser = null;
        }
      }
    },

    updateUserPermissions(userId, fieldPermissions) {
      const index = this.userPermissions.findIndex((p) => p.userId === userId);
      const allFields = this.getAllFieldNames();

      if (index >= 0) {
        const hasAllFields = allFields.every((f) =>
          fieldPermissions.includes(f)
        );

        this.userPermissions[index].fieldPermissions = fieldPermissions;

        // 自动关闭不满足条件的编辑权限
        if (this.userPermissions[index].isEdit && !hasAllFields) {
          this.userPermissions[index].isEdit = false;
        }
      } else {
        this.userPermissions.push({
          userId,
          fieldPermissions,
          isEdit: false,
        });
      }
    },

    selectAllFields() {
      if (!this.selectedUser) return;
      const allFields = this.getAllFieldNames();
      this.updateUserPermissions(this.selectedUser.id, allFields);
    },

    clearAllFields() {
      if (!this.selectedUser) return;
      this.updateUserPermissions(this.selectedUser.id, []);
    },

    selectAllInCategory(category) {
      if (!this.selectedUser) return;

      const currentPermissions = this.getUserPermissions(this.selectedUser.id);
      const categoryFields = category.fields.map((f) => f.fieldName);
      const newPermissions = [
        ...new Set([...currentPermissions, ...categoryFields]),
      ];

      this.updateUserPermissions(this.selectedUser.id, newPermissions);
    },

    clearAllInCategory(category) {
      if (!this.selectedUser) return;

      const currentPermissions = this.getUserPermissions(this.selectedUser.id);
      const categoryFields = category.fields.map((f) => f.fieldName);
      const newPermissions = currentPermissions.filter(
        (field) => !categoryFields.includes(field)
      );

      this.updateUserPermissions(this.selectedUser.id, newPermissions);
    },

    getUserPermissions(userId) {
      const permission = this.userPermissions.find((p) => p.userId === userId);
      return permission ? permission.fieldPermissions : [];
    },

    getUserPermissionCount(userId) {
      return this.getUserPermissions(userId).length;
    },

    getFieldName(fieldKey) {
      for (const category of this.fieldCategories) {
        const field = category.fields.find((f) => f.fieldName === fieldKey);
        if (field) return field.comment;
      }
      return fieldKey;
    },

    removeUserPermissions(userId) {
      this.userPermissions = this.userPermissions.filter(
        (p) => p.userId !== userId
      );
      this.userSelected[userId] = false;
      if (this.selectedUser?.id === userId) {
        this.selectedUser = null;
      }
    },

    removeFieldPermission(userId, fieldKey) {
      const permission = this.userPermissions.find((p) => p.userId === userId);
      if (permission) {
        permission.fieldPermissions = permission.fieldPermissions.filter(
          (f) => f !== fieldKey
        );
      }
    },

    // 新增的编辑权限相关方法
    hasAllFields(userId) {
      const permission = this.userPermissions.find((p) => p.userId === userId);
      if (!permission) return false;

      const allFields = this.getAllFieldNames();
      return allFields.every((field) =>
        permission.fieldPermissions.includes(field)
      );
    },

    getUserEditPermission(userId) {
      const permission = this.userPermissions.find((p) => p.userId === userId);
      return permission ? permission.isEdit : false;
    },

    setUserEditPermission(userId, isEdit) {
      const index = this.userPermissions.findIndex((p) => p.userId === userId);
      if (index >= 0) {
        this.$set(this.userPermissions[index], "isEdit", isEdit);
      }
    },

    getAllFieldNames() {
      return this.fieldCategories.reduce((acc, category) => {
        return acc.concat(category.fields.map((field) => field.fieldName));
      }, []);
    },

    handleConfirm() {
      this.$emit("confirm", this.userPermissions);
      this.userDialogVisible = false;
    },

    getFirstChar(nickname) {
      return nickname ? nickname.charAt(0).toUpperCase() : "";
    },

    getRandomColor(nickname) {
      let hash = 0;
      for (let i = 0; i < nickname.length; i++) {
        hash = nickname.charCodeAt(i) + ((hash << 5) - hash);
      }
      const hue = hash % 360;
      return `hsl(${hue}, 70%, 65%)`;
    },
  },
};
</script>

<style lang="scss" scoped>
.permission-selector {
  display: flex;
  height: 600px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
  background-color: #fff;

  .user-panel,
  .field-panel,
  .preview-panel {
    display: flex;
    flex-direction: column;
    border-right: 1px solid #ebeef5;

    &:last-child {
      border-right: none;
    }
  }

  .user-panel {
    width: 280px;
  }

  .field-panel {
    width: 320px;
  }

  .preview-panel {
    flex: 1;
    min-width: 0;
  }

  .panel-header {
    padding: 12px 16px;
    border-bottom: 1px solid #ebeef5;
    background-color: #f5f7fa;

    h3 {
      margin: 0 0 12px 0;
      font-size: 16px;
      color: #303133;
    }

    .el-input {
      margin-bottom: 4px;
    }

    .actions {
      margin-top: 8px;
      display: flex;
      justify-content: space-between;
    }

    .summary {
      font-size: 12px;
      color: #909399;
      margin-top: 8px;
    }
  }

  .scroll-container {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
  }
}

.user-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 4px;
  position: relative;

  &:hover {
    background-color: #f5f7fa;
  }

  &.active {
    background-color: #ecf5ff;
    box-shadow: 0 0 0 1px #b3d8ff inset;
  }
}

.user-avatar {
  width: 36px;
  height: 36px;
  margin-right: 12px;
  margin-left: 12px;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
  }
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 14px;
}

.user-info {
  flex: 1;
  min-width: 0;

  .nickname {
    font-size: 14px;
    color: #303133;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .dept-name {
    font-size: 12px;
    color: #909399;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.permission-badge {
  margin-left: auto;
  padding: 2px 6px;
  background-color: #f0f9eb;
  color: #67c23a;
  font-size: 12px;
  border-radius: 10px;
}

.field-category {
  margin-bottom: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;

  &:last-child {
    margin-bottom: 0;
  }
}

.category-header {
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h4 {
    margin: 0;
    font-size: 14px;
    color: #606266;
    font-weight: 600;
  }

  .category-actions {
    .el-button {
      padding: 0 4px;
      margin-left: 8px;
    }
  }
}

.category-fields {
  padding: 8px;
}

.field-item {
  padding: 10px 12px;
  border-radius: 4px;
  margin-bottom: 4px;
  transition: all 0.2s;

  &:hover {
    background-color: #f5f7fa;
  }

  &:last-child {
    margin-bottom: 0;
  }

  .field-description {
    font-size: 12px;
    color: #909399;
    margin-left: 24px;
    margin-top: 4px;
  }
}

.preview-item {
  padding: 12px;
  border-bottom: 1px dashed #ebeef5;

  &:last-child {
    border-bottom: none;
  }
}

.preview-user {
  display: flex;
  align-items: center;
  margin-bottom: 8px;

  .user-avatar {
    width: 32px;
    height: 32px;
    margin-right: 8px;
  }

  .user-info {
    flex: 1;

    .nickname {
      font-size: 14px;
    }

    .dept-name {
      font-size: 12px;
    }
  }

  .el-button {
    margin-left: 8px;
    color: #f56c6c;
  }
}

.preview-fields {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  min-height: 32px;

  .el-tag {
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      opacity: 0.8;
    }
  }

  .empty-tip {
    font-size: 12px;
    color: #c0c4cc;
    line-height: 32px;
  }
}

.edit-permission {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px dashed #eee;
  display: flex;
  align-items: center;

  .el-switch {
    margin-right: 8px;
  }

  .edit-tip {
    font-size: 12px;
    color: #909399;
  }
}

.empty-state {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;

  i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  p {
    margin: 0;
    font-size: 14px;
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(144, 147, 153, 0.3);
  border-radius: 3px;

  &:hover {
    background-color: rgba(144, 147, 153, 0.5);
  }
}

::-webkit-scrollbar-track {
  background-color: transparent;
}
</style>