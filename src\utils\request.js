import axios from 'axios'
import {Message, MessageBox, Notification} from 'element-ui'
import store from '@/store'
import {getAccessToken, getRefreshToken, getTenantId, setToken} from '@/utils/auth'
import errorCode from '@/utils/errorCode'
import {getPath, getTenantEnable} from "@/utils/ruoyi";
import {refreshToken} from "@/api/login";

// 需要忽略的提示。忽略後，自動 Promise.reject('error')
const ignoreMsgs = [
  "無效的刷新令牌", // 刷新令牌被刪除時，不用提示
  "刷新令牌已過期" // 使用刷新令牌，刷新獲取新的訪問令牌時，結果因為過期失敗，此時需要忽略。否則，會導致繼續 401，無法跳轉到登出界面
]

// 是否顯示重新登錄
export let isRelogin = { show: false };
// 請求隊列
let requestList = []

axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'
// 創建axios實例
const service = axios.create({
  // axios中請求配置有baseURL選項，表示請求URL公共部分
  baseURL: process.env.VUE_APP_BASE_API + '/admin-api/',
  // 超時
  timeout: 50000,
  // 禁用 Cookie 等信息
  withCredentials: false,
})
// request攔截器
service.interceptors.request.use(config => {
  // 是否需要設置 token
  const isToken = (config.headers || {}).isToken === false
  if (getAccessToken() && !isToken) {
    config.headers['Authorization'] = 'Bearer ' + getAccessToken()
  }
  // 設置租戶
  if (getTenantEnable()) {
    const tenantId = getTenantId();
    if (tenantId) {
      config.headers['tenant-id'] = tenantId;
    }
  }
  // get請求映射params參數
  if (config.method === 'get' && config.params) {
    let url = config.url + '?';
    for (const propName of Object.keys(config.params)) {
      const value = config.params[propName];
      const part = encodeURIComponent(propName) + '='
      if (value !== null && typeof(value) !== "undefined") {
        if (typeof value === 'object') {
          for (const key of Object.keys(value)) {
            let params = propName + '[' + key + ']';
            const subPart = encodeURIComponent(params) + '='
            url += subPart + encodeURIComponent(value[key]) + "&";
          }
        } else {
          url += part + encodeURIComponent(value) + "&";
        }
      }
    }
    url = url.slice(0, -1);
    config.params = {};
    config.url = url;
  }
  return config
}, error => {
    console.log(error)
    Promise.reject(error)
})

// 響應攔截器
service.interceptors.response.use(async res => {
  let { data } = res
  // 未設置狀態碼則默認成功狀態
  // 二進制數據則直接返回，例如說 Excel 導出
  if (
    res.request.responseType === 'blob' ||
    res.request.responseType === 'arraybuffer'
  ) {
    // 注意：如果導出的響應為 json，說明可能失敗了，不直接返回進行下載
    if (res.data.type !== 'application/json') {
      return res.data
    }
    data = await new Response(res.data).json()
  }
  const code = data.code || 200;
  // 獲取錯誤信息
  const msg = data.msg || errorCode[code] || errorCode['default']

  if (ignoreMsgs.indexOf(msg) !== -1) { // 如果是忽略的錯誤碼，直接返回 msg 異常
    return Promise.reject(msg)
  } else if (code === 401) {
    // token 過期直接跳轉到登錄頁面
    return handleAuthorized();
  } else if (code === 500) {
    Message({
      message: msg,
      type: 'error'
    })
    return Promise.reject(new Error(msg))
  } else if (code === 501) {
    Message({
      type: 'error',
      duration: 0,
      message: msg
    })
    return Promise.reject(new Error(msg))
  } else if (code === 901) {
    Message({
      type: 'error',
      duration: 0,
      dangerouslyUseHTMLString: true,
      message: '<div>演示模式，無法進行寫操作</div>'
        + '<div> &nbsp; </div>'
        + '<div>參考 https://doc.iocoder.cn/ 教程</div>'
        + '<div> &nbsp; </div>'
        + '<div>5 分鐘搭建本地環境</div>',
    })
    return Promise.reject(new Error(msg))
  } else if (code !== 200) {
    if (msg === '無效的刷新令牌') {
      console.log(msg)
    } else {
      Notification.error({
        title: msg
      })
    }
    return Promise.reject('error')
  } else {
    return res.data
  }
}, error => {
    console.log('err' + error)
    let {message} = error;
    if (message === "Network Error") {
      message = "後端接口連接異常";
    } else if (message.includes("timeout")) {
      message = "系統接口請求超時";
    } else if (message.includes("Request failed with status code")) {
      message = "系統接口" + message.substr(message.length - 3) + "異常";
    }
    Message({
      message: message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export function getBaseHeader() {
  return {
    'Authorization': "Bearer " + getAccessToken(),
    'tenant-id': getTenantId(),
  }
}

function handleAuthorized() {
  if (!isRelogin.show) {
    isRelogin.show = true;
    MessageBox.confirm('登錄狀態已過期或此賬號於異地登錄，請重新登錄', '系統提示', {
        confirmButtonText: '重新登錄',
        showCancelButton: false, // 隱藏取消按鈕，直接要求重新登錄
        type: 'warning'
      }
    ).then(() => {
      isRelogin.show = false;
      store.dispatch('LogOut').then(() => {
        location.href = getPath('/login');
      })
    }).catch(() => {
      isRelogin.show = false;
      // 即使點擊取消也強制登出
      store.dispatch('LogOut').then(() => {
        location.href = getPath('/login');
      })
    });
  }
  return Promise.reject('無效的會話，或者會話已過期，請重新登錄。')
}

export default service