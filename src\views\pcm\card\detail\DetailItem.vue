<template>
  <div class="detail-item">
    <span class="detail-label">{{ label }}:</span>
    <span class="detail-value">
      <template v-if="isDict && dictType">
        <dict-tag :type="dictType" :value="value" />
      </template>
      <template v-else>
        {{ value }}
      </template>
    </span>
  </div>
</template>

<script>
export default {
  name: 'DetailItem',
  props: {
    label: {
      type: String,
      required: true
    },
    value: {
      type: [String, Number, Boolean, Array],
      default: ''
    },
    isDict: {
      type: Boolean,
      default: false
    },
    dictType: {
      type: String,
      default: ''
    }
  },
}
</script>

<style scoped>
.detail-item {
  display: flex;
  gap: 8px;
  line-height: 1.5;
  
  .detail-label {
    font-weight: 500;
    color: #666;
    min-width: 100px;
    flex-shrink: 0;
  }
  
  .detail-value {
    color: #333;
    word-break: break-word;
  }
}

@media (max-width: 768px) {
  .detail-item {
    flex-direction: column;
    gap: 2px;
    
    .detail-label {
      min-width: auto;
    }
  }
}
</style>