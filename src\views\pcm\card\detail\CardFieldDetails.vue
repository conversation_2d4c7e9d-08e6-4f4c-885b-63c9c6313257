<template>
  <div class="card-details-container" :style="{ maxHeight: maxHeight }">
    <!-- 伙伴資訊 -->
    <div class="section" v-if="hasPartnerInfo">
      <div class="section-header">
        <div class="section-title">
          <i class="el-icon-user"></i>
          {{ $t('partnerInfo') }}
        </div>
      </div>
      <div class="section-content">
        <div class="column">
          <DetailItem  :label="$t('title')" :value="card.title" :is-dict="true"  :dict-type="DICT_TYPE.CARD_TITLE_CN" />
          <DetailItem  :label="$t('firstName')" :value="card.lastName" />
          <DetailItem  :label="$t('lastName')" :value="card.firstName" />
          <DetailItem  :label="$t('honour')" :value="card.honour" />
          <DetailItem @click.native="handlPhoneFn(card.phoneMobile)" :label="$t('phoneMobile')" :value="formatPhone(card.phoneMobileAreaCode, card.phoneMobile)" />
        </div>
        <div class="column">
          <DetailItem  :label="$t('titleEn')" :value="card.titleEn" :is-dict="true"  :dict-type="DICT_TYPE.CARD_TITLE_EN"/>
          <DetailItem :label="$t('firstNameEn')" :value="card.lastNameEn" />
          <DetailItem  :label="$t('lastNameEn')" :value="card.firstNameEn" />
          <DetailItem  :label="$t('honourEn')" :value="card.honourEn" />
           <DetailItem @click.native="handlPhoneFn(card.phoneMainland)" :label="$t('phoneNumberLabel')" :value="formatPhone(card.phoneMainlandAreaCode, card.phoneMainland)" />
        </div>
      </div>
    </div>

    <!-- 组织資訊 -->
    <div class="section" v-if="hasOrganizationInfo">
      <div class="section-header">
        <div class="section-title">
          <i class="el-icon-office-building"></i>
          {{ $t('organizationInfo') }}
        </div>
      </div>
      <div class="section-content">
        <div class="column">
          <DetailItem  :label="$t('companyName')" :value="card.companyName" />
          <DetailItem  :label="$t('department')" :value="card.department" />
          <DetailItem  :label="$t('jobTitle')" :value="card.jobTitle" />
          <DetailItem  :label="$t('email')" :value="card.email" />
          <DetailItem  :label="$t('email')+'1'" v-if="card.email1" :value="card.email1" />
          <DetailItem  :label="$t('email')+'2'" v-if="card.email2" :value="card.email2" />
          <DetailItem @click.native="handlPhoneFn(card.phoneDirectLine)" :label="$t('phoneDirectLine')" :value="formatPhone(card.phoneDirectLineAreaCode, card.phoneDirectLine)" />
          <DetailItem  :label="$t('industryType')" :value="card.industryType" />
          <DetailItem :label="$t('addressLine1')" :value="card.addressLine1" />
          <DetailItem  :label="$t('addressLine2')" :value="card.addressLine2" />
          <DetailItem  :label="$t('district')" :value="card.district" />
          <DetailItem  :label="$t('area')" :value="card.area" />
          <DetailItem  :label="$t('city')" :value="card.city" />
        </div>
        <div class="column">
          <DetailItem :label="$t('companyNameEn')" :value="card.companyNameEn" />
          <DetailItem  :label="$t('departmentEn')" :value="card.departmentEn" />
          <DetailItem  :label="$t('jobTitleEn')" :value="card.jobTitleEn" />
          <DetailItem @click.native="handlPhoneFn(card.phoneOffice)"  :label="$t('phoneOffice')" :value="formatPhone(card.phoneOfficeAreaCode, card.phoneOffice)" />
          <DetailItem  :label="$t('faxNumber')" :value="card.faxNumber" />
          <DetailItem  :label="$t('businessType')" :value="card.businessType!='other' ? card.businessType : card.otherBusiness " :is-dict="card.businessType!='other'" :dict-type="DICT_TYPE.BUSINESS_TYPE"/>
          <DetailItem  :label="$t('addressLine1En')" :value="card.addressLine1En" />
          <DetailItem  :label="$t('addressLine2En')" :value="card.addressLine2En" />
          <DetailItem  :label="$t('districtEn')" :value="card.districtEn" />
          <DetailItem  :label="$t('areaEn')" :value="card.areaEn" />
          <DetailItem  :label="$t('countryEn')" :value="card.countryEn" />
        </div>
      </div>
    </div>

    <!-- 社交媒體 -->
    <div class="section" v-if="hasSocialMedia">
      <div class="section-header">
        <div class="section-title">
          <i class="el-icon-share"></i>
          {{ $t('socialMedia') }}
        </div>
      </div>
      <div class="section-content">
        <div class="column">
          <DetailItem  :label="$t('linkedin')" :value="card.linkedinProfileUrl" />
          <DetailItem  :label="$t('facebook')" :value="card.facebookPageUrl" />
        </div>
        <div class="column">
          <DetailItem  :label="$t('instagram')" :value="card.instagramUrl" />
          <DetailItem  :label="$t('wechat')" :value="card.wechatId" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { parseTime } from '@/utils/ruoyi'
import DetailItem from './DetailItem.vue'

export default {
  name: 'CardDetails',
  components: {
    DetailItem
  },
  props: {
    card: {
      type: Object,
      required: true,
      default: () => ({})
    },
    maxHeight: {
      type: String,
      default: 'none'
    }
  },
  computed: {
    hasPartnerInfo() {
      return this.card.title || this.card.firstName || this.card.lastName || 
             this.card.honour || this.card.phoneMobile || this.card.titleEn || 
             this.card.firstNameEn || this.card.lastNameEn || this.card.honourEn
    },
    hasOrganizationInfo() {
      return this.card.companyName || this.card.department || this.card.jobTitle || 
             this.card.email || this.card.phoneDirectLine || this.card.industryType || 
             this.card.addressLine1 || this.card.addressLine2 || this.card.district || 
             this.card.area || this.card.city || this.card.companyNameEn || 
             this.card.departmentEn || this.card.jobTitleEn || this.card.phoneOffice || 
             this.card.faxNumber || this.card.businessType || this.card.addressLine1En || 
             this.card.addressLine2En || this.card.districtEn || this.card.areaEn || 
             this.card.countryEn
    },
    hasSocialMedia() {
      return this.card.linkedinProfileUrl || this.card.facebookPageUrl || 
             this.card.instagramUrl || this.card.wechatId
    }
  },
  methods: {
    parseTime,
    handlPhoneFn(phoneNumber) {
      if (!phoneNumber) return
      window.location.href = `tel:${phoneNumber}`
    },
      formatPhone(areaCode, number) {
    // 确保areaCode和number都不为null/undefined
    const safeAreaCode = areaCode || '';
    const safeNumber = number || '';
    
    // 如果两者都有值才拼接，否则返回有值的那个
    return safeAreaCode && safeNumber 
      ? `${safeAreaCode}${safeNumber}`
      : safeAreaCode || safeNumber || '-';
  },
  }
}
</script>

<style lang="scss" scoped>
.card-details-container {
  overflow-y: auto;
  padding: 0 12px 12px 0;
  
  .section {
    margin-bottom: 28px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    padding: 16px;
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .section-header {
      margin-bottom: 16px;
      
      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        display: flex;
        align-items: center;
        
        i {
          margin-right: 8px;
        }
      }
    }
    
    .section-content {
      display: flex;
      gap: 32px;
      
      .column {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 14px;
      }
    }
  }
  
  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: rgba(144, 147, 153, 0.3);
    border-radius: 3px;
    
    &:hover {
      background-color: rgba(144, 147, 153, 0.5);
    }
  }
  
  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
  
  @media (max-width: 768px) {
    padding: 0 8px 8px 0;
    
    .section {
      padding: 12px;
      margin-bottom: 20px;
      
      .section-content {
        flex-direction: column;
        gap: 14px;
        
        .column {
          gap: 12px;
        }
      }
    }
  }
}
</style>