<template>
  <div class="app-container">
    <doc-alert title="支付功能开启" url="https://doc.iocoder.cn/pay/build/" />
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="应用名" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入应用名" clearable
                  @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="开启状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择开启状态" clearable>
          <el-option v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)" :key="parseInt(dict.value)" :label="dict.label"
                     :value="parseInt(dict.value)"/>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   v-hasPermi="['pay:app:create']">新增
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="应用编号" align="center" prop="id"/>
      <el-table-column label="应用名" align="center" prop="name"/>
      <el-table-column label="开启状态" align="center" prop="status">
        <template v-slot="scope">
          <el-switch v-model="scope.row.status" :active-value="0" :inactive-value="1"
                     @change="handleStatusChange(scope.row)"/>
        </template>
      </el-table-column>
      <el-table-column label="支付宝配置" align="center">
        <el-table-column :label="payChannelEnum.ALIPAY_APP.name" align="center">
          <template v-slot="scope">
            <el-button type="success" icon="el-icon-check" circle
                       v-if="isChannelExists(scope.row.channelCodes, payChannelEnum.ALIPAY_APP.code)"
                       @click="handleChannel(scope.row, payChannelEnum.ALIPAY_APP.code)">
            </el-button>
            <el-button v-else type="danger" icon="el-icon-close" circle
                       @click="handleChannel(scope.row, payChannelEnum.ALIPAY_APP.code)">
            </el-button>
          </template>
        </el-table-column>
        <el-table-column :label="payChannelEnum.ALIPAY_PC.name" align="center">
          <template v-slot="scope">
            <el-button type="success" icon="el-icon-check" circle
                       v-if="isChannelExists(scope.row.channelCodes, payChannelEnum.ALIPAY_PC.code)"
                       @click="handleChannel(scope.row, payChannelEnum.ALIPAY_PC.code)">
            </el-button>
            <el-button v-else type="danger" icon="el-icon-close" circle
                       @click="handleChannel(scope.row, payChannelEnum.ALIPAY_PC.code)">
            </el-button>
          </template>
        </el-table-column>
        <el-table-column :label="payChannelEnum.ALIPAY_WAP.name" align="center">
          <template v-slot="scope">
            <el-button type="success" icon="el-icon-check" circle
                       v-if="isChannelExists(scope.row.channelCodes, payChannelEnum.ALIPAY_WAP.code)"
                       @click="handleChannel(scope.row, payChannelEnum.ALIPAY_WAP.code)">
            </el-button>
            <el-button v-else type="danger" icon="el-icon-close" circle
                       @click="handleChannel(scope.row, payChannelEnum.ALIPAY_WAP.code)">
            </el-button>
          </template>
        </el-table-column>
        <el-table-column :label="payChannelEnum.ALIPAY_QR.name" align="center">
          <template v-slot="scope">
            <el-button type="success" icon="el-icon-check" circle
                       v-if="isChannelExists(scope.row.channelCodes, payChannelEnum.ALIPAY_QR.code)"
                       @click="handleChannel(scope.row, payChannelEnum.ALIPAY_QR.code)">
            </el-button>
            <el-button v-else type="danger" icon="el-icon-close" circle
                       @click="handleChannel(scope.row, payChannelEnum.ALIPAY_QR.code)">
            </el-button>
          </template>
        </el-table-column>
        <el-table-column :label="payChannelEnum.ALIPAY_BAR.name" align="center">
          <template v-slot="scope">
            <el-button type="success" icon="el-icon-check" circle
                       v-if="isChannelExists(scope.row.channelCodes, payChannelEnum.ALIPAY_BAR.code)"
                       @click="handleChannel(scope.row,payChannelEnum.ALIPAY_BAR.code)">
            </el-button>
            <el-button v-else type="danger" icon="el-icon-close" circle
                       @click="handleChannel(scope.row, payChannelEnum.ALIPAY_BAR.code)">
            </el-button>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="微信配置" align="center">
        <el-table-column :label="payChannelEnum.WX_LITE.name" align="center">
          <template v-slot="scope">
            <el-button type="success" icon="el-icon-check" circle
                       v-if="isChannelExists(scope.row.channelCodes, payChannelEnum.WX_LITE.code)"
                       @click="handleChannel(scope.row, payChannelEnum.WX_LITE.code)">
            </el-button>
            <el-button v-else type="danger" icon="el-icon-close" circle
                       @click="handleChannel(scope.row, payChannelEnum.WX_LITE.code)">
            </el-button>
          </template>
        </el-table-column>
        <el-table-column :label="payChannelEnum.WX_PUB.name" align="center">
          <template v-slot="scope">
            <el-button type="success" icon="el-icon-check" circle
                       v-if="isChannelExists(scope.row.channelCodes, payChannelEnum.WX_PUB.code)"
                       @click="handleChannel(scope.row, payChannelEnum.WX_PUB.code)">
            </el-button>
            <el-button v-else type="danger" icon="el-icon-close" circle
                       @click="handleChannel(scope.row, payChannelEnum.WX_PUB.code)">
            </el-button>
          </template>
        </el-table-column>
        <el-table-column :label="payChannelEnum.WX_APP.name" align="center">
          <template v-slot="scope">
            <el-button type="success" icon="el-icon-check" circle
                       v-if="isChannelExists(scope.row.channelCodes, payChannelEnum.WX_APP.code)"
                       @click="handleChannel(scope.row, payChannelEnum.WX_APP.code)">
            </el-button>
            <el-button v-else type="danger" icon="el-icon-close" circle
                       @click="handleChannel(scope.row, payChannelEnum.WX_APP.code)">
            </el-button>
          </template>
        </el-table-column>
        <el-table-column :label="payChannelEnum.WX_NATIVE.name" align="center">
          <template v-slot="scope">
            <el-button type="success" icon="el-icon-check" circle
                       v-if="isChannelExists(scope.row.channelCodes, payChannelEnum.WX_NATIVE.code)"
                       @click="handleChannel(scope.row, payChannelEnum.WX_NATIVE.code)">
            </el-button>
            <el-button v-else type="danger" icon="el-icon-close" circle
                       @click="handleChannel(scope.row, payChannelEnum.WX_NATIVE.code)">
            </el-button>
          </template>
        </el-table-column>
        <el-table-column :label="payChannelEnum.WX_BAR.name" align="center">
          <template v-slot="scope">
            <el-button type="success" icon="el-icon-check" circle
                       v-if="isChannelExists(scope.row.channelCodes, payChannelEnum.WX_BAR.code)"
                       @click="handleChannel(scope.row, payChannelEnum.WX_BAR.code)">
            </el-button>
            <el-button v-else type="danger" icon="el-icon-close" circle
                       @click="handleChannel(scope.row, payChannelEnum.WX_BAR.code)">
            </el-button>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="模拟支付配置" align="center">
        <el-table-column :label="payChannelEnum.MOCK.name" align="center">
          <template v-slot="scope">
            <el-button type="success" icon="el-icon-check" circle
                       v-if="isChannelExists(scope.row.channelCodes, payChannelEnum.MOCK.code)"
                       @click="handleChannel(scope.row, payChannelEnum.MOCK.code)">
            </el-button>
            <el-button v-else type="danger" icon="el-icon-close" circle
                       @click="handleChannel(scope.row, payChannelEnum.MOCK.code)">
            </el-button>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="钱包支付配置" align="center">
        <el-table-column :label="payChannelEnum.WALLET.name" align="center">
          <template v-slot="scope">
            <el-button type="success" icon="el-icon-check" circle
                       v-if="isChannelExists(scope.row.channelCodes, payChannelEnum.WALLET.code)"
                       @click="handleChannel(scope.row, payChannelEnum.WALLET.code)">
            </el-button>
            <el-button v-else type="danger" icon="el-icon-close" circle
                       @click="handleChannel(scope.row, payChannelEnum.WALLET.code)">
            </el-button>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['pay:app:update']">修改
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['pay:app:delete']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="160px">
        <el-form-item label="应用名" prop="name">
          <el-input v-model="form.name" placeholder="请输入应用名"/>
        </el-form-item>
        <el-form-item label="开启状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.COMMON_STATUS)" :key="parseInt(dict.value)" :label="parseInt(dict.value)">
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="支付结果的回调地址" prop="orderNotifyUrl">
          <el-input v-model="form.orderNotifyUrl" placeholder="请输入支付结果的回调地址"/>
        </el-form-item>
        <el-form-item label="退款结果的回调地址" prop="refundNotifyUrl">
          <el-input v-model="form.refundNotifyUrl" placeholder="请输入退款结果的回调地址"/>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 对话框（支付应用的配置） -->
    <weixin-channel-form ref="weixinChannelFormRef" @success="getList" />
    <alipay-channel-form ref="alipayChannelFormRef" @success="getList" />
    <none-config-channel-form ref="noneConfigChannelFormRef" @success="getList" />
  </div>
</template>

<script>
import { createApp, updateApp, changeAppStatus, deleteApp, getApp, getAppPage } from "@/api/pay/app";
import { PayChannelEnum, CommonStatusEnum } from "@/utils/constants";
import weixinChannelForm from "@/views/pay/app/components/weixinChannelForm";
import alipayChannelForm from "@/views/pay/app/components/alipayChannelForm";
import noneConfigChannelForm from '@/views/pay/app/components/noneConfigChannelForm';

export default {
  name: "PayApp",
  components: {
    weixinChannelForm,
    alipayChannelForm,
    noneConfigChannelForm
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 支付应用信息列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        status: null,
        createTime: []
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [{required: true, message: "应用名不能为空", trigger: "blur"}],
        status: [{required: true, message: "开启状态不能为空", trigger: "blur"}],
        orderNotifyUrl: [{required: true, message: "支付结果的回调地址不能为空", trigger: "blur"}],
        refundNotifyUrl: [{required: true, message: "退款结果的回调地址不能为空", trigger: "blur"}],
      },
      // 支付渠道枚举
      payChannelEnum: PayChannelEnum,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      getAppPage(this.queryParams).then(response => {
        this.list = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        status: CommonStatusEnum.ENABLE,
        remark: undefined,
        orderNotifyUrl: undefined,
        refundNotifyUrl: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加支付应用信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getApp(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改支付应用信息";
      });
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text = row.status === CommonStatusEnum.ENABLE ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.name + '"应用吗?').then(function () {
        return changeAppStatus(row.id, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function () {
        row.status = row.status === CommonStatusEnum.ENABLE ? CommonStatusEnum.DISABLE
          : CommonStatusEnum.ENABLE;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (!valid) {
          return;
        }
        // 修改的提交
        if (this.form.id != null) {
          updateApp(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        createApp(this.form).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除支付应用信息编号为"' + row.id + '"的数据项?').then(function () {
        return deleteApp(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    /**
     * 修改支付渠道信息
     */
    handleChannel(row, code) {
      if (code.indexOf('alipay_') === 0) {
        this.$refs['alipayChannelFormRef'].open(row.id, code);
        return
      }
      if (code.indexOf('wx_') === 0) {
        this.$refs['weixinChannelFormRef'].open(row.id, code);
        return
      }
      if (code === 'mock') {
        this.$refs['noneConfigChannelFormRef'].open(row.id, code);
        return
      }
      if (code === 'wallet') {
        this.$refs['noneConfigChannelFormRef'].open(row.id, code);
        return
      }
    },
    /**
     * 根据渠道编码判断渠道列表中是否存在
     *
     * @param channels 渠道列表
     * @param channelCode 渠道编码
     */
    isChannelExists(channels, channelCode) {
      return channels && channels.indexOf(channelCode) !== -1;
    }
  }
}
</script>
