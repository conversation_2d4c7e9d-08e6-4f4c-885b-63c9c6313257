<template>
  <el-dialog
    :visible.sync="visible"
    title="图片裁剪"
    width="680px"
    @close="handleClose"
    class="image-cropper-dialog"
    @opened="modalOpened"
  >
    <div class="cropper-container">
      <div class="cropper-wrapper">
        <vue-cropper
          ref="cropper"
          :img="imageSrc"
          :autoCrop="true"
          :autoCropWidth="200"
          :autoCropHeight="200"
          :fixedBox="true"
          :info="true"
          :centerBox="true"
          :high="true"
          @realTime="realTime"
          v-if="cropperVisible"
        ></vue-cropper>
      </div>
      
      <div class="operation-area">
        <el-button-group>
          <el-button size="small" @click="changeScale(-1)">
            <i class="el-icon-zoom-out"></i>
            缩小
          </el-button>
          <el-button size="small" @click="changeScale(1)">
            <i class="el-icon-zoom-in"></i>
            放大
          </el-button>
          <el-button size="small" @click="rotateLeft">
            <i class="el-icon-refresh-left"></i>
            向左旋转
          </el-button>
          <el-button size="small" @click="rotateRight">
            <i class="el-icon-refresh-right"></i>
            向右旋转
          </el-button>
          <el-button 
            size="small" 
            @click="handleReset" 
          >
            <i class="el-icon-refresh"></i>
            重置
          </el-button>
        </el-button-group>
      </div>
      
      <div class="preview-area">
        <div class="preview-title">预览</div>
        <div class="preview-image" :style="previewStyle"></div>
      </div>
    </div>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="confirmCrop">确认</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { VueCropper } from 'vue-cropper'

export default {
  components: { VueCropper },
  data() {
    return {
      visible: false,
      cropperVisible: false,
      imageSrc: '',
      file: null,
      previews: {},
      previewStyle: {}
    }
  },
  methods: {
    open(file) {
      this.file = file
      const reader = new FileReader()
      reader.onload = e => {
        this.imageSrc = e.target.result
        this.visible = true
      }
      reader.readAsDataURL(file)
    },
    
    modalOpened() {
      this.cropperVisible = true
    },
    
    rotateLeft() {
      this.$refs.cropper.rotateLeft()
    },
    
    rotateRight() {
      this.$refs.cropper.rotateRight()
    },
    
    changeScale(num) {
      this.$refs.cropper.changeScale(num)
    },
    
    handleReset() {
      this.$refs.cropper.reset()
    },
    
    realTime(data) {
      this.previews = data
      this.previewStyle = {
        width: data.w + 'px',
        height: data.h + 'px',
        transform: data.transform,
        overflow: 'hidden',
        margin: '0 auto'
      }
    },
    
    confirmCrop() {
      this.$refs.cropper.getCropBlob(blob => {
        const croppedFile = new File([blob], this.file.name, {
          type: this.file.type,
          lastModified: Date.now()
        })
        this.$emit('cropped', croppedFile)
        this.visible = false
      })
    },
    
    handleClose() {
      this.cropperVisible = false
      if (this.imageSrc) {
        URL.revokeObjectURL(this.imageSrc)
      }
      this.resetState()
    },
    
    resetState() {
      this.imageSrc = ''
      this.file = null
      this.previews = {}
    }
  }
}
</script>

<style lang="scss" scoped>
.image-cropper-dialog {
  .el-dialog__body {
    padding: 15px 20px;
  }
}

.cropper-container {
  display: flex;
  flex-direction: column;
  height: 500px;
}

.cropper-wrapper {
  flex: 1;
  height: 350px;
  background: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.operation-area {
  margin: 15px 0;
  display: flex;
  justify-content: center;
  
  .el-button-group {
    display: flex;
    gap: 1px;
  }
  
  .el-button {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 15px;
    
    i {
      font-size: 14px;
    }
  }
}

.preview-area {
  margin-top: 15px;
  padding: 10px;
  border: 1px dashed #ddd;
  border-radius: 4px;
  text-align: center;
  
  .preview-title {
    margin-bottom: 10px;
    font-size: 14px;
    color: #666;
  }
  
  .preview-image {
    max-width: 200px;
    max-height: 200px;
    margin: 0 auto;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.el-button[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>