<script>
export default {
  name: "FixedUploadCard",
  methods: {
    openCardModalEvent() {
      this.$emit("openCardModalEvent");
    },
  },
};
</script>

<template>
  <div class="upload-card-page">
    <div class="flex-btn-concat">
      <el-button
        @click="openCardModalEvent"
        type="success"
        icon="el-icon-plus"
        >{{ $t("uploadCard") }}</el-button
      >
    </div>
  </div>
</template>

<style lang="scss" scoped>
.upload-card-page {
  position: fixed;
  bottom: 10%;
  left: 0;
  right: 0;
  .flex-btn-concat {
    display: flex;
    justify-content: center;
  }
}
</style>
