<template>
  <div :style="{ padding: '0' }" class="app-container">
    <el-row :gutter="20">
      <el-col :span="6" :xs="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>{{$t('personalInfo')}}</span>
          </div>
          <div>
            <div class="text-center">
              <userAvatar :user="user" />
            </div>
            <ul class="list-group list-group-striped">
              <li class="list-group-item">
                <svg-icon icon-class="user" />{{$t('username')}}
                <div class="pull-right">{{ user.username }}</div>
              </li>
              <!-- <li class="list-group-item">
                <svg-icon icon-class="phone" />{{$t('phoneNumber')}}
                <div class="pull-right">{{ user.mobile }}</div>
              </li> -->
              <li class="list-group-item">
                <svg-icon icon-class="email" />{{$t('userEmail')}}
                <div class="pull-right">{{ user.email }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="tree" />{{$t('department')}}
                <div class="pull-right" v-if="user.dept">{{ user.dept.name }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="tree" />{{$t('jobPosition')}}
                <div class="pull-right" v-if="user.posts">{{ user.posts.map(post => post.name).join(',') }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="peoples" />{{$t('belongRole')}}
                <div class="pull-right" v-if="user.roles">{{ user.roles.map(role => role.name).join(',') }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="date" />{{$t('creationDate')}}
                <div class="pull-right">{{ parseTime(user.createTime) }}</div>
              </li>
            </ul>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18" :xs="24">
        <el-card>
          <div slot="header" class="clearfix">
            <span>{{$t('basicInfo')}}</span>
          </div>
          <el-tabs v-model="activeTab">
            <el-tab-pane :label="$t('basicInfo')" name="userinfo">
              <userInfo :user="user" />
            </el-tab-pane>
            <el-tab-pane :label="$t('changePassword')" name="resetPwd">
              <resetPwd :user="user" />
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import userAvatar from "./userAvatar";
import userInfo from "./userInfo";
import resetPwd from "./resetPwd";
import userSocial from "./userSocial";
import { getUserProfile } from "@/api/system/user";

export default {
  name: "Profile",
  components: { userAvatar, userInfo, resetPwd, userSocial },
  data() {
    return {
      user: {},
      roleGroup: {},
      postGroup: {},
      activeTab: "userinfo"
    };
  },
  created() {
    this.getUser();
  },
  methods: {
    getUser() {
      getUserProfile().then(response => {
        this.user = response.data;
      });
    },
    setActiveTab(activeTab) {
      this.activeTab = activeTab
    }
  }
};
</script>
