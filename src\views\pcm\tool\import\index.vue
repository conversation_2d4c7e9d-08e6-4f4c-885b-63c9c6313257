<template>
  <div class="import-container">
    <div class="import-steps">
      <!-- Step 1: Download template -->
      <div class="step">
        <div class="step-header">
          <span class="step-number">{{ $t("step1") }}</span>
          <span class="step-title">{{ $t("downloadTemplate") }}</span>
        </div>
        <div class="step-content">
          <div class="download-box" @click="downloadTemplate">
            <div class="excel-icon">
              <i class="el-icon-document"></i>
            </div>
            <div class="download-text">{{ $t("clickDownloadTemplate") }}</div>
          </div>
        </div>
      </div>

      <!-- Step 2: Confirm format -->
      <div class="step">
        <div class="step-header">
          <span class="step-number">{{ $t("step2") }}</span>
          <div class="title-wrapper">
            <span class="step-title">{{ $t("confirmTemplateFormat") }}</span>
            <a href="#" class="learn-more" target="_blank">{{
              $t("learnMore")
            }}</a>
          </div>
        </div>
      </div>

      <!-- Step 3: Upload file -->
      <div class="step">
        <div class="step-header">
          <span class="step-number">{{ $t("step3") }}</span>
          <span class="step-title">{{ $t("importExcelNotice") }}</span>
        </div>
        <div class="step-content">
          <p class="upload-tip">{{ $t("fileSizeLimit") }}</p>
          <el-upload
            class="upload-btn"
            :action="upload.url + '?updateSupport=' + upload.updateSupport"
            :before-upload="beforeUpload"
            :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess"
            :on-error="handleUploadError"
            :headers="upload.headers"
            :auto-upload="true"
            :show-file-list="false"
            :disabled="upload.isUploading"
            accept=".xls,.xlsx"
          >
            <el-button
              type="primary"
              size="medium"
              :loading="upload.isUploading"
            >
              <template #icon>
                <i class="el-icon-upload"></i>
              </template>
              {{ upload.isUploading ? "卡片导入中" : $t("selectExcelFile") }}
            </el-button>
          </el-upload>

          <!-- Enhanced loading overlay -->
          <div v-if="upload.isUploading" class="upload-loading-overlay">
            <div class="loading-content">
              <el-progress
                type="circle"
                :percentage="upload.progressPercent"
                :width="80"
                :stroke-width="8"
              />
              <p class="loading-text">卡片導入中，請稍後...</p>
              <p class="progress-text">{{ upload.progressPercent }}%</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { importTemplate } from "@/api/pcm/card";
import { getBaseHeader } from "@/utils/request";
export default {
  data() {
    return {
      upload: {
        isUploading: false,
        progressPercent: 0,
        updateSupport: 0,
        headers: getBaseHeader(),
        url: process.env.VUE_APP_BASE_API + "/admin-api/pcm/card/import",
      },
    };
  },
  methods: {
    downloadTemplate() {
      window.location.href = "#";
      this.$message.success(this.$t("templateDownloadStarted"));
      importTemplate().then((response) => {
        this.$download.excel(response, "名片上傳Excel模版.xls");
      });
    },
    beforeUpload(file) {
      if (this.upload.isUploading) {
        this.$message.warning("已有文檔正在上傳中");
        return false;
      }

      const isExcel =
        file.type === "application/vnd.ms-excel" ||
        file.type ===
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isExcel) {
        this.$message.error(this.$t("uploadExcelOnly"));
        return false;
      }
      if (!isLt10M) {
        this.$message.error(this.$t("fileSizeLimitExceeded"));
        return false;
      }

      this.upload.progressPercent = 0;
      return true;
    },
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
      // 只更新到99%，留1%给数据处理
      this.upload.progressPercent = Math.min(99, Math.floor(event.percent));
    },
    async handleFileSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$modal.msgError(response.msg);
        this.upload.isUploading = false;
        this.upload.progressPercent = 0;
        return;
      }

      // 先设置为99%，表示正在处理数据
      this.upload.progressPercent = 99;

      // 强制UI更新
      await this.$nextTick();

      // 添加一个微小延迟，确保用户能看到99%的状态
      await new Promise((resolve) => setTimeout(resolve, 50));

      // 处理数据
      let data = response.data;
      let text = "創建成功名片數量：" + data.createCardNames.length;
      for (const cardFullName of data.createCardNames) {
        text += "<br />&nbsp;&nbsp;&nbsp;&nbsp;" + cardFullName;
      }
      text += "<br />更新成功名片數量：" + data.updateCardNames.length;
      for (const cardFullName of data.updateCardNames) {
        text += "<br />&nbsp;&nbsp;&nbsp;&nbsp;" + cardFullName;
      }
      text +=
        "<br />更新失敗名片數量：" + Object.keys(data.failureCardNames).length;
      for (const cardFullName in data.failureCardNames) {
        text +=
          "<br />&nbsp;&nbsp;&nbsp;&nbsp;" +
          cardFullName +
          "：" +
          data.failureCardNames[cardFullName];
      }

      // 设置为100%
      this.upload.progressPercent = 100;

      // 再次强制UI更新
      await this.$nextTick();
      await new Promise((resolve) => setTimeout(resolve, 50));

      // 关闭加载状态并显示结果
      this.upload.isUploading = false;

      this.$alert(
        `<div style="
      display: flex;
      flex-direction: column;
      height: 60vh;
      max-height: 500px;
    ">
      <div style="
        flex: 1;
        overflow-y: auto;
        padding: 10px 0;
      ">${text}</div>
    </div>`,
        "導入結果",
        {
          dangerouslyUseHTMLString: true,
          customClass: "fixed-result-alert",
          confirmButtonText: "確定",
        }
      );
    },
    handleUploadError(err, file, fileList) {
      this.upload.isUploading = false;
      this.upload.progressPercent = 0;
      this.$message.error("文件上传失败");
      console.error("Upload error:", err);
    },
  },
};
</script>

<style scoped>
.import-container {
  max-width: 800px;
  padding: 24px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.step {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px dashed #e4e7ed;
}

.step:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.step-header {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.step-number {
  font-size: 16px;
  font-weight: 500;
  color: #409eff;
  margin-right: 12px;
  white-space: nowrap;
}

.title-wrapper {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.step-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-right: 12px;
}

.download-box {
  width: 200px;
  height: 200px;
  background-color: #67c23a;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  color: white;
}

.download-box:hover {
  background-color: #5daf34;
  transform: translateY(-2px);
}

.excel-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.download-text {
  font-size: 16px;
  text-align: center;
}

.learn-more {
  color: #409eff;
  text-decoration: none;
  font-size: 14px;
  white-space: nowrap;
}

.learn-more:hover {
  text-decoration: underline;
}

.upload-tip {
  color: #909399;
  font-size: 14px;
  margin-bottom: 16px;
}

.upload-btn {
  margin-top: 8px;
}

/* Enhanced loading overlay styles */
.upload-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.95);
  z-index: 2000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-content {
  text-align: center;
  padding: 30px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 300px;
}

.loading-text {
  margin-top: 20px;
  font-size: 16px;
  color: #606266;
  font-weight: 500;
}

.progress-text {
  margin-top: 10px;
  font-size: 14px;
  color: #409eff;
  font-weight: bold;
}

.el-button {
  min-width: 160px;
}
.fixed-result-alert {
  display: flex;
  flex-direction: column;
  max-height: 70vh;
}
.fixed-result-alert .el-message-box__header {
  position: sticky;
  top: 0;
  background: white;
  z-index: 10;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}
.fixed-result-alert .el-message-box__content {
  padding: 0;
  margin: 0;
}
.fixed-result-alert .el-message-box__btns {
  position: sticky;
  bottom: 0;
  background: white;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}
.el-progress-circle {
  transition: stroke-dashoffset 0.3s ease;
}
</style>