export default {
  language: 'English', // 语言切换按钮的文本
  ssoLoggingIn: 'Logging in via SSO...',
  systemPrompt: 'System Prompt',
  confirmLogout: 'Are you sure to log out and exit the system?',
  loginToHKYouthAssociation: 'Log in to Hong Kong Youth Association',
  emailOrPhone: 'Email/Phone Number',
  password: 'Password',
  rememberMe: 'Remember Me',
  forgotPassword: 'Forgot Password',
  continue: 'Continue',
  or: 'Or',
  //左边菜单栏
  MyCalendar: "My Calendar",
  WorkflowProfile: "Personal Profile",
  Card: "Card Store",
  Company: "Company",
  Meeting: "Negotiation Records",
  Colleague: "Colleague",
  Setting: "Settings",
  menu: 'Function List',
  //顶部
  personalInfo: 'Personal Information',
  logout: 'Logout',

  titleDefault: 'title',
  allCards: 'All Cards',
  advancedFilter: 'Advanced Filter',
  clearFilter: 'Clear Filter',
  uploadCard: 'Upload Business Card',
  tag: 'Tag',
  shareCard: 'Share Business Card',
  delete: 'Delete',
  modifyCreator: 'Modify Card Creator',
  exportExcel: 'Export to Excel',
  mergeCards: 'Merge Business Cards',
  name: 'Name',
  cardImage: 'Business Card Image',
  companyPosition: 'Company/Position',
  contactInfo: 'Contact Information',
  creator: 'Creator',
  action: 'Action',
  selectTag: 'Select Tag',
  companyTagManagement: 'Company Tag Management',
  uploadCardImage: 'Upload Business Card Image',
  cancel: 'Cancel',
  confirm: 'Confirm',

  total: 'Total',
  businessCardsUnit: 'Business Cards',
  action: 'Action',
  businessCardType: 'Type',
  convertEmailSignature: 'How to Convert Email Signature to Business Card',
  learnMore: 'Learn More',
  sortByDate: 'Sort by Date',
  sortByName: 'Sort by Name',
  sortByCompany: 'Sort by Company',
  continueUploading: 'Continue Uploading',
  excelImport: "Excel Batch Import",
  manualInput: "Manual Input",

  cardInformation: 'Business Card Information',
  by: 'By',
  add_: 'add',
  cardCreator: 'Card Creator',
  addToCompanyCardFolder: 'Add to Company Card Folder',
  manualVerification: 'Manual Verification',
  manuallyEdited: 'Manually Edited',
  editTags: 'Edit Tags',
  addNegotiationRecord: 'Negotiation Record',
  colleaguesWithViewPermission: 'Colleagues with View Permission',
  departmentsWithViewPermission: 'Departments with View Permission',
  viewPermissionNote: 'The card creator\'s supervisor and administrators all have permission to view this card',
  searchHint: 'Search by name, company/title/email/phone number, etc.',
  advancedFilter: 'Advanced Filter',
  creationTimeFilter: 'Creation Time Filter',
  cardCreator: 'Business Card Creator',
  tags: 'Tags',
  to: 'To',
  selectCreator: 'Select Creator',
  validationStatus: 'Verification Status',
  showMoreKeywords: 'Show More Keywords',
  hideKeywords: 'Hide Keywords',
  enterMoreKeywords: "You can specify multiple search terms by inserting ';' (semicolon)",
  allCompanies: 'All Companies',
  enterCompanyName: 'Enter Company Name',
  organization: 'Organization',
  businessCards: 'Business Cards',
  negotiationRecords: 'Negotiation Records',
  all: 'All',
  myNegotiations: 'My Negotiations',
  subordinateNegotiations: 'Subordinates\' Negotiations',
  viewByDepartment: 'View by Department',
  searchNegotiations: 'Enter keywords to search negotiation records',
  colleague: 'Colleague',
  profile: 'Profile',
  colleagueBusinessCard: 'Business Card',
  edit: 'Edit',
  myQRCode: 'My QR Code',
  myECardLink: 'My E-Card Link',
  settings: 'Settings',
  permissionManagement: 'Permission Management',
  roleManagement: 'Role Management',
  departmentManagement: 'Department Management',
  staffManagement: 'Staff Management',
  emailAccount: 'Email Account',
  emailTemplate: 'Email Template',
  emailLogs: 'Email Logs',
  apiSuccessLogs: 'API Success Logs',
  apiErrorLogs: 'API Error Logs',
  dictionaryType: 'Dictionary Type',
  dictionaryData: 'Dictionary Data',
  menuName: 'Menu Name',
  status: 'Status',
  search: 'Search',
  reset: 'Reset',
  add: 'Add',
  modify: 'Modify',
  delete: 'Delete',
  expandCollapse: 'Expand/Collapse',
  icon: 'Icon',
  sort: 'Sort',
  permissionId: 'Permission ID',
  componentPath: 'Component Path',
  componentName: 'Component Name',
  operation: 'Operation',
  hideSearch: 'Hide Search',
  refresh: 'Refresh',
  departmentName: 'Department Name',
  manager: 'Manager',
  creationTime: 'Creation Time',
  menuStatus: 'Menu Status',
  parentMenu: 'Parent Menu',
  directory: 'Directory',
  button: 'Button',
  menuIcon: 'Menu Icon',
  displayOrder: 'Display Order',
  routePath: 'Route Path',
  isVisible: 'Visibility',
  visible: 'Visible',
  hidden: 'Hidden',
  alwaysShow: 'Always Show',
  always: 'Always',
  no: 'No',
  parentDepartment: 'Parent Department',
  departmentStatus: 'Department Status',
  email: 'Email',
  username: 'Username',
  enterUsername: 'Enter Username',
  phoneNumber: 'Phone Number',
  enterPhoneNumber: 'Enter Phone Number',
  userStatus: 'User Status',
  startTime: 'Start Time',
  endTime: 'End Time',
  userId: 'User ID',
  nickname: 'Nickname',
  more: 'More',
  resetPassword: 'Reset Password',
  assignRole: 'Assign Role',
  affiliatedDepartment: 'Affiliated Department',
  gender: 'Gender',
  position: 'Position',
  remark: 'Remark',
  enterEmail: 'Enter Email',
  id: 'ID',
  smtpDomain: 'SMTP Domain',
  smtpPort: 'SMTP Port',
  enableSSL: 'Enable SSL',
  templateName: 'Template Name',
  enterTemplateName: 'Enter Template Name',
  templateCode: 'Template Code',
  enterTemplateCode: 'Enter Template Code',
  enterEmailAccount: 'Enter Email Account',
  enableStatus: 'Enable Status',
  selectEnableStatus: 'Select Enable Status',
  templateTitle: 'Template Title',
  templateContent: 'Template Content',
  senderName: 'Sender Name',
  test: 'Test',
  recipientEmail: 'Recipient Email',
  parameters: 'Parameters',
  enterRecipientEmail: 'Enter Recipient Email',
  templateId: 'Template ID',
  enterTemplateId: 'Enter Template ID',
  sendStatus: 'Send Status',
  selectSendStatus: 'Select Send Status',
  sendTime: 'Send Time',
  userType: 'User Type',
  selectUserType: 'Select User Type',
  requestUrl: 'Request URL',
  enterRequestUrl: 'Enter Request URL',
  resultCode: 'Result Code',
  enterResultCode: 'Enter Result Code',
  logId: 'Log ID',
  requestMethod: 'Request Method',
  requestTime: 'Request Time',
  executionDuration: 'Execution Duration',
  operationResult: 'Operation Result',
  operationModule: 'Operation Module',
  operationName: 'Operation Name',
  operationType: 'Operation Type',
  logKey: 'Log Key',
  traceId: 'Trace ID',
  appName: 'Application Name',
  userInfo: 'User Info',
  requestInfo: 'Request Info',
  requestParams: 'Request Params',
  exceptionTime: 'Exception Time',
  exceptionName: 'Exception Name',
  handleStatus: 'Handle Status',
  details: 'Details',
  processed: 'Processed',
  processor: "Processor",
  processingTime: "Processing Time",
  ignored: 'Ignored',
  export: 'Export',
  dictionaryName: 'Dictionary Name',
  enterDictionaryName: 'Enter Dictionary Name',
  enterDictionaryType: 'Enter Dictionary Type',
  selectDictionaryStatus: 'Select Dictionary Status',
  dictionaryLabel: 'Dictionary Label',
  enterDictionaryLabel: 'Enter Dictionary Label',
  dictionaryCode: 'Dictionary Code',
  dictionaryKey: 'Dictionary Key',
  dictionarySort: 'Dictionary Sort',
  colorType: 'Color Type',
  toolbox: 'Toolbox',
  exportToExcel: 'Export to Excel',
  batchImportCards: 'Batch Import Cards',
  changePassword: 'Change Password',
  companyTagManagement: 'Company Tag Management',
  cardRecycleBin: 'Card Recycle Bin',
  exportAllNotice: 'All cards will be exported as Excel file',
  exportFilterNotice: 'You can also use advanced filter to export specific cards',
  exportCompanyCards: 'Export Company Cards to Excel',
  step1: 'Step 1',
  step2: 'Step 2',
  step3: 'Step 3',
  downloadTemplate: 'Download Template',
  clickDownloadTemplate: 'Click to download Excel template',
  confirmTemplateFormat: 'Please ensure your data format matches the template',
  learnMore: 'Learn More',
  importExcelNotice: 'Import your Excel contacts file',
  fileSizeLimit: 'Recommended <10MB, <10,000 contacts',
  selectExcelFile: 'Select Excel file to upload',
  basicInfo: 'Basic Info',
  userEmail: 'User Email',
  department: 'Department',
  belongRole: 'Role',
  creationDate: 'Creation Date',
  oldPassword: 'Old Password',
  newPassword: 'New Password',
  confirmPassword: 'Confirm Password',
  modifyAvatar: 'Change Avatar',
  select: 'Select',
  submit: 'Submit',
  jobPosition: 'Job Position',
  enterOldPassword: 'Please enter old password',
  enterNewPassword: 'Please enter new password',
  confirmPassword: 'Please confirm password',
  save: 'Save',
  close: 'Close',
  enterTagName: 'Enter Tag Name',
  permanentlyDelete: 'Permanently Delete',
  restore: 'Restore',
  deletedBy: 'Deleted By',

  editCard: 'Edit Card',
  addCard: 'Add Card',
  saveAndEditNext: 'Save & Edit Next',
  partnerInfo: 'Partner Information',
  organizationInfo: 'Organization Information',
  socialMedia: 'Social Media',
  type: 'Type',
  content: 'Content',
  enterContent: 'Please enter content',
  uploadHint: 'JPG/PNG under 5MB allowed',
  time: 'Time',
  selectTime: 'Select Time',
  location: 'Location',
  addLocationOptional: 'Add Location (Optional)',
  exportStarted: 'Export started, please wait...',
  templateDownloadStarted: 'Template download started',
  uploadExcelOnly: 'Only Excel files can be uploaded!',
  fileSizeLimitExceeded: 'File size cannot exceed 10MB!',
  uploadSuccessProcessing: 'File uploaded successfully, processing data...',
  uploadFailed: 'File upload failed',
  selectUser: 'Select User',
  selectDepartment: 'Select Department',
  viewSelectedOnly: 'View Selected Only',
  supervisorLabel: "Supervisor",
  supervisorNoSelection: "No Supervisor",
  exportPermissionLabel: "Allow Export",
  fileRecord: 'File Record',
  fileConfig: 'File Configuration',
  configName: 'Config Name',
  storage: 'Storage',
  serialNumber: 'Serial Number',
  mainConfig: 'Main Config',
  test: 'Test',
  addFileConfig: 'Add File Config',
  modifyConfig: "Modify Configuration",
  enterConfigName: 'Please enter config name',
  selectStorage: 'Please select storage',
  enterRemark: 'Please enter remarks',
  filePath: 'File Path',
  fileName: 'File Name',
  fileURL: 'File URL',
  fileSize: 'File Size',
  fileType: 'File Type',
  fileContent: 'File Content',
  uploadTime: 'Upload Time',
  enterFilePath: 'Please enter file path',
  uploadFile: 'Upload File',
  // Role Management
  roleName: 'Role Name',
  roleKey: 'Role Key',
  enterRoleName: 'Please enter role name',
  enterRoleKey: 'Please enter role key',
  selectRoleStatus: 'Please select role status',
  roleId: 'Role ID',
  roleType: 'Role Type',
  menuPermissions: 'Menu Permissions',
  roleOrder: 'Role Order',
  addRole: 'Add Role',
  editRole: 'Edit Role',

  // Menu Management
  routePath: 'Route Path',
  isVisible: 'Visible',
  enterMenuName: 'Please enter menu name',
  enterRoutePath: 'Please enter route path',
  menuStatus: 'Menu Status',
  alwaysShow: 'Always Show',
  permissionString: 'Permission String',
  componentPath: 'Component Path',
  componentName: 'Component Name',
  isCached: 'Is Cached',
  addMenu: 'Add Menu',
  editMenu: 'Edit Menu',

  // Department Management
  enterDeptName: 'Please enter department name',
  selectDeptStatus: 'Please select department status',
  selectLeader: 'Please select leader',
  selectParentDept: 'Please select parent department',
  enterEmail: 'Please enter email',
  enterPhone: 'Please enter phone number',
  enterPersonInCharge: "Please enter the person in charge",
  addDept: 'Add Department',
  editDept: 'Edit Department',
  import: 'Import',

  // User Management
  enterNickname: 'Please enter nickname',
  selectAffiliatedDept: 'Please select affiliated department',
  userPassword: 'User Password',
  userGender: 'Gender',
  position: 'Position',
  pleaseSelect: 'Please select',
  addUser: 'Add User',
  editUser: 'Edit User',
  assignRoles: 'Assign Roles',
  role: 'Role',
  enterUserId: 'Please enter user ID',

  // Logs
  selectProcessStatus: 'Please select process status',
  apiAccessLogDetail: 'API Access Log Details',
  apiErrorLogDetail: 'API Error Log Details',

  // Dictionary
  dictId: 'Dictionary ID',
  enterRemarkContent: 'Please enter remark content',
  editDictType: 'Edit Dictionary Type',
  addDictType: 'Add Dictionary Type',
  editDictData: 'Edit Dictionary Data',
  addDictData: 'Add Dictionary Data',
  selectDataStatus: 'Please select data status',
  enterDataKey: 'Please enter data key',
  startFilter: 'Start Filter',
  // User Authentication
  enterUserPassword: 'Please enter user password',
  password: 'Password',
  enterPassword: 'Please enter password',

  // Path Configuration
  basePath: 'Base Path',
  enterBasePath: 'Please enter base path',

  // Host Configuration
  hostAddress: 'Host Address',
  enterHostAddress: 'Please enter host address',
  hostPort: 'Host Port',
  enterHostPort: 'Please enter host port',

  // Connection Settings
  username: 'Username',
  connectionMode: 'Connection Mode',
  activeMode: 'Active Mode',
  passiveMode: 'Passive Mode',

  // Node Configuration
  nodeAddress: 'Node Address',
  enterNodeAddress: 'Please enter node address',

  // Storage Configuration
  storageBucket: 'Storage Bucket',
  enterBucket: 'Please enter bucket',
  enterAccessKey: 'Please enter access key',
  enterAccessSecret: 'Please enter access secret',

  // Domain Configuration
  customDomain: 'Custom Domain',
  enterCustomDomain: 'Please enter custom domain',
  uploadFile: "Upload File",
  dragAndDrop: "Drag and drop files here, or",
  clickToUpload: "click to upload",
  uploadTip: "Note: Only JPG, PNG, GIF files are allowed!",
  uploadSuccess: "Upload Success",
  uploadFailed: "Upload Failed",
  fileTypeError: "Unsupported file type",
  fileSizeExceeded: "File size exceeds limit",
  // Personal fields
  title: 'Title',
  firstName: 'First Name',
  lastName: 'Last Name',
  honour: 'Honour',
  phoneMobile: 'Mobile Phone',
  titleEn: 'Title (English)',
  firstNameEn: 'First Name (English)',
  lastNameEn: 'Last Name (English)',
  honourEn: 'Honour (English)',
  phoneNumberLabel: 'Mainland Number',

  // Organization fields
  companyName: 'Company Name',
  department: 'Department',
  jobTitle: 'Job Title',
  email: 'Email',
  phoneDirectLine: 'Direct Line',
  industryType: 'Industry Type',
  addressLine1: 'Address Line 1',
  addressLine2: 'Address Line 2',
  district: 'District',
  area: 'Area',
  city: 'City',
  companyNameEn: 'Company Name (English)',
  departmentEn: 'Department (English)',
  jobTitleEn: 'Job Title (English)',
  phoneOffice: 'Office Phone',
  faxNumber: 'Fax Number',
  businessType: 'Business Type',
  addressLine1En: 'Address Line 1 (English)',
  addressLine2En: 'Address Line 2 (English)',
  districtEn: 'District (English)',
  areaEn: 'Area (English)',
  countryEn: 'Country (English)',

  // Social media
  linkedin: 'LinkedIn',
  facebook: 'Facebook',
  instagram: 'Instagram',
  wechat: 'WeChat',

  selectUserAndFields: 'Select User and Fields',
  userList: 'User List',
  searchUser: 'Search User',
  fieldList: 'Field List',
  selectAll: 'Select All',
  clearAll: 'Clear All',
  selectUserFirst: 'Please select a user first',
  permissionPreview: 'Permission Preview',
  noUserSelected: 'No user selected',
  permissionCount: '{count} permissions',
  noPermissionAssigned: 'No field permissions assigned',
  permissionSaved: 'Permissions saved successfully',
  selectedUsersCount: 'Selected {count} users',
  totalPermissionsCount: 'Total {count} permissions',
  requireAllFieldsForEdit: "All fields must be selected to enable edit permission",
  phoneFormatError: "Please enter a valid Hong Kong phone number (8 or 9 digits, may include +852)",
  emailFormatError: "Please enter a valid email address",
  card: {
    uploadCardImage: 'Upload Business Card',
    uploadNewCard: 'Upload New Card',
    frontSide: 'Front Side',
    frontSideRequired: 'Front(Required)',
    backSide: 'Back Side',
    backSideOptional: 'Back(Optional)',
    completeUploadTip: 'Please complete unfinished card uploads (cards with gray background are missing front side image)',
    selectedCount: 'Selected {count} cards',
    invalidFileType: 'Please check the image format, only PNG, JPG, JPEG are supported',
    fileSizeExceeded: 'Please upload images less than {size}MB',
    uploadBackSideConfirm: 'Do you want to upload the back side of the card?',
    uploadBackSide: 'Upload Back Side',
    deleteConfirm: 'Are you sure you want to delete this card?',
    selectAtLeastOne: 'Please select at least one complete card',
    completeUploadFirst: 'Please complete unfinished card uploads first',
    uploadFailed: 'Upload failed',
    deleteFailed: 'Failed to delete card',
    recognitionFailed: 'Card recognition failed',
    recognitionResult: 'Card Recognition Result',
    successfulCards: 'Successfully Recognized Cards ({count})',
    failedCards: 'Failed Recognition Cards ({count})',
    keepSelected: 'Keep Selected',
    retryFront: 'Retry Front Side',
    retryBack: 'Retry Back Side',
    retry: 'Retry',
    recognizingCards: 'Recognizing business cards, please wait...',
    duplicateCards: "Duplicate Cards ({count})",
    existingCardInfo: "Existing Card Information",
    viewDetail: "View Details",
    name: "Name",
    company: "Company",
    title: "Title",
    phone: "Phone",
    creator: "Creator",
    createdAt: "Created At"
  },
  common: {
    selectAll: 'Select All',
    tip: 'Tip',
    notNow: 'Not Now',
    deleteSuccess: 'Deleted successfully',
    confirmAndContinue: 'Confirm and Continue',
    unknown: "Unknown"
  },
  cardPreview: {
    front: 'Front',
    back: 'Back',
    flip: 'Flip sides',
    zoomIn: 'Zoom in',
    zoomOut: 'Zoom out',
    rotateLeft: 'Rotate left',
    rotateRight: 'Rotate right',
    reset: 'Reset',
    close: 'Close',
    flipHint: 'Flip available'
  },
  upload: {
    failed: 'Upload failed',
    deleteFailed: 'File deletion failed',
    imageTypeError: 'Only JPG/PNG/GIF images are allowed!',
    imageSizeError: 'Image size cannot exceed {size}MB!',
    limitExceeded: 'Limit of {limit} files exceeded. {selected} files already selected, attempting to upload {attempted} more'
  },
  meeting: {
    updateSuccess: 'Meeting updated successfully',
    updateFailed: 'Failed to update meeting',
    addSuccess: 'Meeting added successfully',
    addFailed: 'Failed to add meeting'
  },
  businessCard: {
    frontSide: 'Front Side',
    backSide: 'Back Side',
    deleteFront: 'Delete Front',
    deleteBack: 'Delete Back',
    preview: 'Preview',
    uploadFront: 'Upload Front Image',
    uploadBack: 'Upload Back Image',
    uploadHint: 'JPG/PNG format, max 5MB',
    previewTitle: 'Image Preview',
    deleteConfirm: 'Are you sure to delete this image?',
    confirm: 'Confirm',
    cancel: 'Cancel',
    deleted: 'Deleted',
    uploadSuccess: 'Upload successful',
    uploadFailed: 'Upload failed',
    imageTypeError: 'Only image files are allowed',
    sizeLimitError: 'Image size cannot exceed 5MB',
    tip: 'Tip',
    uploading: "Uploading..."
  },
  errors: {
    phone: {
      hkAreaCode: 'Please enter correct HK area code (e.g. +852)',
      hkNumber: 'Please enter 8-9 digit phone number',
      mainlandAreaCode: 'Please enter correct Mainland area code (e.g. +86)',
      mainlandNumber: 'Please enter 11-digit valid Mainland phone number (starting with 1)',
      required: 'Please enter phone number',
       invalidNumber: "Invalid phone number",
      invalidFormat: "Incorrect phone number format",
      invalidAreaCode: "Invalid country/region code",
      mismatch: "Country code does not match the number"
    },
    fields: {
      phoneMobile: 'Mobile Phone',
      phoneMainland: 'Mainland Number',
      phoneOffice: 'Office Phone',
      phoneDirectLine: 'Direct Line'
    }
  },
  companyManagement: {
    confirmDeleteCompany: 'Are you sure you want to delete this company? This action cannot be undone!',
    warning: 'Warning',
    deleteSuccess: 'Delete successful',
    deleteFailed: 'Delete failed',
    deleteCancelled: 'Delete cancelled',
    deleteButton: 'Delete',
    cancelButton: 'Cancel'
  },
  exportApproval:'Excel Export Approval',
    approvalId: 'Approval ID',
    enterApprovalId: 'Please enter approval ID',
    applicant: 'Applicant',
    enterApplicant: 'Please enter applicant',
    applyTime: 'Apply Time',
    approvalStatus: 'Approval Status',
    selectApprovalStatus: 'Please select approval status',
    exportStatus: 'Export Status',
    selectExportStatus: 'Please select export status',
    pendingApproval: 'Pending Approval',
    pendingExport: 'Pending Export',
    exportCount: 'Export Count',
    items: 'items',
    exportRemark: 'Export Remark',
    approvalRemark: 'Approval Remark',
    approver: 'Approver',
    approvalTime: 'Approval Time',
    approvalDialogTitle: 'Export Approval',
    enterApprovalRemark: 'Please enter approval remark',
    approvalRemarkRequired: 'Approval remark is required',
    confirmApprove: 'Are you sure to approve this export request?',
    confirmReject: 'Are you sure to reject this export request?',
    approveSuccess: 'Approve successfully',
    rejectSuccess: 'Reject successfully',
    confirmExport: 'Are you sure to export these {0} items?',
    exportSuccess: 'Export successfully',
    approve: 'Approve',
    reject: 'Reject',
    noData: "No data available",
    excelExportApplication: 'Excel Export Application',
    remarks: 'Remarks',
    pleaseEnterRemarks: 'Please enter remarks (optional)',
    todoNotification: {
      title: 'Todo List',
      takeAction: 'Take Action',
      ignore: 'Ignore'
    }



};
