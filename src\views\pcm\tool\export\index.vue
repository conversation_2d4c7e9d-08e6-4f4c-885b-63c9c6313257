<template>
  <div class="export-container">
    <div class="export-content">
      <div class="export-tip">
        <p class="tip-text">{{ $t("exportAllNotice") }}</p>
        <p class="tip-text">{{ $t("exportFilterNotice") }}</p>
      </div>

      <el-button type="primary" class="export-btn" @click="handleExportCheck">
        <img src="@/assets/pcm/tool/icon-excel-export.png" class="btn-icon" />
        {{ $t("exportCompanyCards") }}
      </el-button>
    </div>

    <excel-export-dialog
      ref="excelExportDialog"
      :approver="approver"
      :export-count="exportCount"
      :query-params="queryParams"
    />
  </div>
</template>

<script>
import ExcelExportDialog from "@/views/pcm/card/table/ExcelExportDialog";
import { exportCardExcel } from "@/api/pcm/card";
import { checkRestriction } from "@/api/pcm/approval";
export default {
  components: {
    ExcelExportDialog,
  },
  data() {
    return {
      queryParams: {},
      approver: "", // 审批人姓名
      exportCount: 0, // 导出数量
      exportLoading: false, // 新增導出loading狀態
    };
  },
  methods: {
    /**
     * 检查用户导出是否符合条件
     */
    handleExportCheck() {
      checkRestriction({
        ...this.queryParams,
        ids: this.selectedIds,
      })
        .then((res) => {
          if (res.data.isRestricted) {
            // 周期内超出50条，需要审批
            this.approver = res.data.approverName; // 设置审批人
            this.exportCount = res.data.exportTotal; // 设置导出数量

            this.$modal.confirm(res.data.msg).then(() => {
              this.$refs.excelExportDialog.open();
            });
          } else {
            this.handleExport();
          }
        })
        .catch((error) => {
          console.error("检查导出限制出错:", error);
          this.$message.error("檢查導出權限出錯");
        });
    },

    handleExport() {
      this.exportLoading = true; // 開始導出，顯示loading

      const loadingInstance = this.$loading({
        lock: true,
        text: "正在導出文件，請稍候...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      exportCardExcel({})
        .then((res) => {
          this.$message.success(this.$t("exportStarted"));
          this.$download.excel(res, "卡片數據.xls");
        })
        .catch((error) => {
          console.error("導出文件出錯:", error);
          this.$message.error("導出文件失敗");
        })
        .finally(() => {
          this.exportLoading = false; // 導出完成，隱藏loading
          loadingInstance.close();
        });
    },
  },
};
</script>

<style scoped>
.export-container {
  padding: 24px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  text-align: left;
}

.export-content {
  max-width: 600px;
  margin: 0;
}

.export-tip {
  margin-bottom: 32px;
}

.tip-text {
  color: #606266;
  font-size: 14px;
  line-height: 1.8;
  margin: 8px 0;
  text-align: left;
}

.export-btn {
  padding: 12px 24px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: fit-content;
}

.btn-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}
</style>
