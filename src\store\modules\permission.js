import { generateRoutes } from "@/utils/menu";
import { constantRoutes } from '@/router'


const permission = {
    state: {
        routes: [], // 存储动态生成的路由
        addRoutes: [] // 存储通过 addRoutes 添加的路由
    },

    mutations: {
        SET_ROUTES(state, routes) {
            state.addRoutes = routes
            state.routes = constantRoutes.concat(routes) // 合并静态和动态路由
        }
    },

    actions: {
        // 生成动态路由
        GenerateRoutes({ commit }, menus) {
            return new Promise(resolve => {
                const accessedRoutes = generateRoutes(menus) // 生成动态路由
                accessedRoutes.push({path: '*', redirect: '/404', hidden: true})
                commit('SET_ROUTES', accessedRoutes)
                resolve(accessedRoutes)
            })
        }
    },
}

export default permission;