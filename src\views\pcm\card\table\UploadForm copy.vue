<template>
  <div class="search-form-container">
    <image-cropper ref="cropper" @cropped="handleCroppedFile" />

    <base-dialog
      v-model="uploadDialogVisible"
      :title="$t('card.uploadCardImage')"
      width="800px"
      @confirm="handleUploadConfirm"
      :confirm-loading="isUploadingCards"
    >
      <template v-slot:content>
        <div class="card-upload-container">
          <div class="toolbar">
            <el-checkbox
              v-model="allSelected"
              :indeterminate="isIndeterminate"
              @change="handleSelectAll"
              :disabled="isUploadingImage"
            >
              {{ $t("common.selectAll") }}
            </el-checkbox>

            <el-button
              icon="el-icon-plus"
              type="primary"
              :disabled="isUploadingImage"
              @click="startNewCardUpload"
              :loading="isUploadingImage"
              >{{ $t("card.uploadNewCard") }}</el-button
            >
          </div>

          <input
            ref="fileInput"
            type="file"
            accept="image/*"
            style="display: none"
            @change="handleFileChange"
            :disabled="isUploadingImage"
          />

          <div class="card-list">
            <div
              v-for="(card, index) in cards"
              :key="index"
              class="card-item"
              :class="{
                selected: isCardSelected(card),
                invalid: !card.frontFileId,
                uploading: card.isUploading,
              }"
            >
              <el-checkbox
                v-model="card.checked"
                :disabled="!card.frontFileId || isUploadingImage"
                @change="handleCardSelect(card)"
                class="card-checkbox"
              />

              <div class="card-images">
                <!-- 正面 -->
                <div class="card-side front-side">
                  <template v-if="card.frontFileId">
                    <img
                      :src="getFileUrl(card.frontFileId)"
                      :alt="$t('card.frontSide')"
                      @click="editCardSide(index, 'front')"
                    />
                    <div
                      v-if="card.isUploading && card.uploadingSide === 'front'"
                      class="upload-progress"
                    >
                      <el-progress
                        :percentage="card.uploadProgress"
                        :stroke-width="4"
                        :show-text="false"
                      />
                      <div class="progress-text">
                        {{ card.uploadProgress }}%
                      </div>
                    </div>
                  </template>
                  <div
                    v-else
                    class="empty-side"
                    @click="editCardSide(index, 'front')"
                  >
                    <i class="el-icon-picture"></i>
                    <span>{{ $t("card.frontSideRequired") }}</span>
                  </div>
                  <i
                    v-if="!card.frontFileId"
                    class="el-icon-warning required-icon"
                  ></i>
                </div>

                <!-- 背面 -->
                <div class="card-side back-side">
                  <template v-if="card.backFileId">
                    <img
                      :src="getFileUrl(card.backFileId)"
                      :alt="$t('card.backSide')"
                      @click="editCardSide(index, 'back')"
                    />
                    <div
                      v-if="card.isUploading && card.uploadingSide === 'back'"
                      class="upload-progress"
                    >
                      <el-progress
                        :percentage="card.uploadProgress"
                        :stroke-width="4"
                        :show-text="false"
                      />
                      <div class="progress-text">
                        {{ card.uploadProgress }}%
                      </div>
                    </div>
                  </template>
                  <div
                    v-else
                    class="empty-side"
                    @click="editCardSide(index, 'back')"
                  >
                    <i class="el-icon-picture"></i>
                    <span>{{ $t("card.backSideOptional") }}</span>
                  </div>
                </div>
              </div>

              <i
                class="el-icon-delete delete-card"
                @click="deleteCard(index)"
                :class="{ disabled: isUploadingImage }"
              ></i>
            </div>
          </div>

          <div class="footer-tips">
            <div v-if="hasInvalidCards" class="invalid-tip">
              <i class="el-icon-warning"></i>
              <span>{{ $t("card.completeUploadTip") }}</span>
            </div>
            <div class="selected-count">
              {{ $t("card.selectedCount", { count: selectedCards.length }) }}
            </div>
          </div>
        </div>
      </template>
    </base-dialog>

    <!-- 识别结果弹窗 -->
    <base-dialog
      v-model="resultDialogVisible"
      :title="$t('card.recognitionResult')"
      width="800px"
      :show-cancel="false"
      :show-confirm="hasFailedCards"
      :confirm-text="$t('common.confirmAndContinue')"
      :confirm-loading="isProcessingResult"
      @confirm="handleResultConfirm"
    >
      <template v-slot:content>
        <div class="recognition-result-container">
          <div
            v-if="successCards.length > 0"
            class="result-section success-section"
          >
            <div class="section-title">
              <i class="el-icon-success"></i>
              <span>{{
                $t("card.successfulCards", { count: successCards.length })
              }}</span>
            </div>
            <div class="card-result-list">
              <div
                v-for="card in successCards"
                :key="card.cardId"
                class="card-result-item"
              >
                <div class="card-info">
                  <img
                    :src="getFileUrl(card.frontFileId)"
                    class="card-thumbnail"
                  />
                  <span class="card-id">ID: {{ card.cardId }}</span>
                </div>
                <div class="card-message">
                  <i class="el-icon-success"></i>
                  <span>{{ card.message }}</span>
                </div>
              </div>
            </div>
          </div>

          <div
            v-if="failedCards.length > 0"
            class="result-section failed-section"
          >
            <div class="section-title">
              <i class="el-icon-warning"></i>
              <span>{{
                $t("card.failedCards", { count: failedCards.length })
              }}</span>
            </div>
            <div class="card-result-list">
              <div
                v-for="card in failedCards"
                :key="card.frontFileId"
                class="card-result-item"
              >
                <div class="card-info">
                  <img
                    :src="getFileUrl(card.frontFileId)"
                    class="card-thumbnail"
                  />
                  <el-checkbox
                    v-model="card.keepSelected"
                    class="retry-checkbox"
                  >
                    {{ $t("card.keepSelected") }}
                  </el-checkbox>
                </div>
                <div class="card-message">
                  <i :class="statusIcon(card.status)"></i>
                  <span>{{ card.message }}</span>
                </div>
                <div class="card-actions">
                  <el-button
                    size="mini"
                    @click="retryUpload(card)"
                    :loading="card.isRetrying"
                  >
                    {{ retryButtonText(card.status) }}
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </base-dialog>

    <!-- 添加全局遮罩和Loading -->
    <div v-show="isUploadingCards" class="uploading-overlay">
      <div class="uploading-content">
        <i class="el-icon-loading loading-icon"></i>
        <div class="uploading-text">{{ $t("card.recognizingCards") }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import ImageCropper from "@/components/ImageCropper";
import BaseDialog from "@/components/PcmDialog";
import { deleteFile } from "@/api/infra/file.js";
import { doubleSidedCard, carUpload } from "@/api/pcm/card";

export default {
  components: { BaseDialog, ImageCropper },
  data() {
    return {
      uploadDialogVisible: false,
      resultDialogVisible: false,
      cards: [],
      allSelected: false,
      isIndeterminate: false,
      currentUpload: {
        cardIndex: null,
        side: "front",
      },
      allFiles: {},
      processedCards: [],
      successCards: [],
      failedCards: [],
      isUploadingImage: false,
      isUploadingCards: false,
      isProcessingResult: false,
    };
  },
  computed: {
    selectedCards() {
      return this.cards
        .filter((card) => card.checked && card.frontFileId)
        .map((card) => ({
          frontFileId: card.frontFileId,
          backFileId: card.backFileId || null,
        }));
    },
    hasInvalidCards() {
      return this.cards.some((card) => !card.frontFileId);
    },
    hasFailedCards() {
      return this.failedCards.length > 0;
    },
  },
  methods: {
    open() {
      this.uploadDialogVisible = true;
      this.cards = [];
      this.allSelected = false;
      this.isIndeterminate = false;
    },

    startNewCardUpload() {
      if (this.isUploadingImage) return;

      const newCard = {
        frontFileId: null,
        backFileId: null,
        checked: false,
        isUploading: false,
        uploadingSide: null,
        uploadProgress: 0,
      };
      this.cards.push(newCard);
      this.currentUpload.cardIndex = this.cards.length - 1;
      this.currentUpload.side = "front";
      this.$refs.fileInput.click();
    },

    editCardSide(cardIndex, side) {
      if (this.isUploadingImage) return;

      this.currentUpload.cardIndex = cardIndex;
      this.currentUpload.side = side;
      this.$refs.fileInput.click();
    },

    handleFileChange(event) {
      const files = event.target.files;
      if (files.length === 0 || this.isUploadingImage) return;
      const file = files[0];
      console.log("上传图片大小:-->" + file.size);
      if (!this.validateFile(file)) return;

      this.$refs.cropper.open(file);
      event.target.value = "";
    },

    validateFile(file) {
      const validTypes = ["image/jpeg", "image/png", "image/jpg"];
      if (!validTypes.includes(file.type.toLowerCase())) {
        this.$message.warning(this.$t("card.invalidFileType"));
        return false;
      }

      const maxSize = 5 * 1024 * 1024;
      if (file.size > maxSize) {
        this.$message.warning(
          this.$t("card.fileSizeExceeded", { size: maxSize / (1024 * 1024) })
        );
        return false;
      }

      return true;
    },

    async handleCroppedFile(croppedFile) {
      console.log("裁剪后图片大小:-->" + croppedFile.size);
      const { cardIndex, side } = this.currentUpload;

      try {
        this.isUploadingImage = true;
        this.cards[cardIndex].isUploading = true;
        this.cards[cardIndex].uploadingSide = side;
        this.cards[cardIndex].uploadProgress = 0;

        const formData = new FormData();
        formData.append("files", croppedFile);

        const response = await carUpload(formData, {
          onUploadProgress: (progressEvent) => {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
            this.cards[cardIndex].uploadProgress = percentCompleted;
          },
        });

        const uploadedFile = response.data[0];
        this.allFiles[uploadedFile.id] = uploadedFile;

        this.cards[cardIndex][`${side}FileId`] = uploadedFile.id;

        if (side === "front") {
          this.cards[cardIndex].checked = true;
          this.updateSelectStatus();

          this.$confirm(
            this.$t("card.uploadBackSideConfirm"),
            this.$t("common.tip"),
            {
              confirmButtonText: this.$t("card.uploadBackSide"),
              cancelButtonText: this.$t("common.notNow"),
              type: "info",
            }
          )
            .then(() => {
              this.editCardSide(cardIndex, "back");
            })
            .catch(() => {});
        }
      } catch (error) {
        console.error("Upload failed:", error);
        this.$message.error(this.$t("card.uploadFailed"));
      } finally {
        this.cards[cardIndex].isUploading = false;
        this.cards[cardIndex].uploadingSide = null;
        this.isUploadingImage = false;
      }
    },

    getFileUrl(fileId) {
      return this.allFiles[fileId]?.url || "";
    },

    isCardSelected(card) {
      return card.checked && card.frontFileId;
    },

    handleCardSelect(card) {
      if (!card.checked) {
        this.allSelected = false;
      }
      this.updateSelectStatus();
    },

    handleSelectAll(val) {
      this.cards.forEach((card) => {
        if (card.frontFileId) {
          card.checked = val;
        }
      });
      this.isIndeterminate = false;
    },

    updateSelectStatus() {
      const validCards = this.cards.filter((card) => card.frontFileId);
      const checkedCount = validCards.filter((card) => card.checked).length;

      this.allSelected =
        checkedCount === validCards.length && validCards.length > 0;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < validCards.length;
    },

    async deleteCard(index) {
      try {
        await this.$confirm(
          this.$t("card.deleteConfirm"),
          this.$t("common.tip"),
          {
            type: "warning",
          }
        );

        const card = this.cards[index];
        if (card.frontFileId) await deleteFile(card.frontFileId);
        if (card.backFileId) await deleteFile(card.backFileId);

        this.cards.splice(index, 1);
        this.updateSelectStatus();
        this.$message.success(this.$t("common.deleteSuccess"));
      } catch (error) {
        if (error !== "cancel") {
          console.error("Delete failed:", error);
          this.$message.error(this.$t("card.deleteFailed"));
        }
      }
    },

    async handleUploadConfirm() {
      // 防止重复提交
      if (this.isUploadingCards) return;

      if (this.selectedCards.length === 0) {
        this.$message.warning(this.$t("card.selectAtLeastOne"));
        return;
      }

      if (this.hasInvalidCards) {
        this.$message.warning(this.$t("card.completeUploadFirst"));
        return;
      }

      try {
        this.isUploadingCards = true;
        const res = await doubleSidedCard({ fileIds: this.selectedCards });

        if (res.data.processedCards) {
          this.processResult(res.data.processedCards);
          this.resultDialogVisible = true;
        } else {
          this.$router.push({
            name: "CardEdit",
            params: { cardId: res.data[0] },
          });
          this.uploadDialogVisible = false;
        }
      } catch (error) {
        console.error("Recognition failed:", error);
        this.$message.error(this.$t("card.recognitionFailed"));
      } finally {
        this.isUploadingCards = false;
      }
    },

    processResult(processedCards) {
      processedCards.forEach((card) => {
        card.keepSelected = card.cardId == null;
        card.isRetrying = false;
      });

      this.successCards = processedCards.filter((card) => card.cardId != null);
      this.failedCards = processedCards.filter((card) => card.cardId == null);
      this.updateCardSelection(processedCards);
    },

    updateCardSelection(processedCards) {
      this.cards.forEach((card) => {
        const processedCard = processedCards.find(
          (pc) => pc.frontFileId === card.frontFileId
        );

        if (processedCard && processedCard.cardId == null) {
          card.checked = false;
        }
      });

      this.updateSelectStatus();
    },

    statusIcon(status) {
      return (
        {
          FRONT_FAILED: "el-icon-error",
          BACK_FAILED: "el-icon-warning",
        }[status] || "el-icon-info"
      );
    },

    retryButtonText(status) {
      return (
        {
          FRONT_FAILED: this.$t("card.retryFront"),
          BACK_FAILED: this.$t("card.retryBack"),
        }[status] || this.$t("card.retry")
      );
    },

    async retryUpload(card) {
      const cardIndex = this.cards.findIndex(
        (c) => c.frontFileId === card.frontFileId
      );

      if (cardIndex !== -1) {
        try {
          card.isRetrying = true;
          const side = card.status === "FRONT_FAILED" ? "front" : "back";
          this.editCardSide(cardIndex, side);
          this.resultDialogVisible = false;
        } finally {
          card.isRetrying = false;
        }
      }
    },

    async handleResultConfirm() {
      try {
        this.isProcessingResult = true;

        this.failedCards.forEach((card) => {
          if (!card.keepSelected) {
            const cardIndex = this.cards.findIndex(
              (c) => c.frontFileId === card.frontFileId
            );
            if (cardIndex !== -1) {
              this.cards[cardIndex].checked = false;
            }
          }
        });

        if (this.successCards.length > 0) {
          const firstSuccessCard = this.successCards[0];
          this.$router.push({
            name: "CardEdit",
            params: { cardId: firstSuccessCard.cardId },
          });
          this.uploadDialogVisible = false;
        } else {
          this.resultDialogVisible = false;
        }
      } finally {
        this.isProcessingResult = false;
      }
    },
  },
};
</script>

<style scoped>
.card-upload-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-height: 600px;
  overflow-y: auto;
  padding: 10px;
}

.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

.card-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.card-item {
  position: relative;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 15px;
  transition: all 0.3s;
}

.card-item.selected {
  border-color: #409eff;
  box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.2);
}

.card-item.invalid {
  background-color: #f5f7fa;
}

.card-checkbox {
  position: absolute;
  top: 5px;
  left: 5px;
  z-index: 1;
}

.card-images {
  display: flex;
  gap: 10px;
}

.card-side {
  flex: 1;
  height: 150px;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  border: 1px dashed #dcdfe6;
}

.front-side::before,
.back-side::before {
  content: "";
  position: absolute;
  top: 5px;
  left: 5px;
  padding: 2px 5px;
  font-size: 12px;
  color: white;
  border-radius: 2px;
}

.front-side::before {
  content: "正面";
  background-color: #67c23a;
}

.back-side::before {
  content: "反面";
  background-color: #e6a23c;
}

.card-side img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.empty-side {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  cursor: pointer;
}

.empty-side i {
  font-size: 24px;
  margin-bottom: 8px;
}

.empty-side span {
  font-size: 12px;
}

.required-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  color: #f56c6c;
  opacity: 0.7;
}

.delete-card {
  position: absolute;
  top: 5px;
  right: 5px;
  color: #f56c6c;
  cursor: pointer;
  font-size: 18px;
}

.delete-card:hover {
  color: #f78989;
}

.footer-tips {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #ebeef5;
}

.invalid-tip {
  color: #e6a23c;
  font-size: 12px;
  display: flex;
  align-items: center;
}

.invalid-tip i {
  margin-right: 5px;
}

.selected-count {
  font-size: 12px;
  color: #909399;
}

/* 识别结果样式 */
.recognition-result-container {
  max-height: 500px;
  overflow-y: auto;
}

.result-section {
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid #ebeef5;
}

.section-title i {
  margin-right: 8px;
  font-size: 18px;
}

.success-section .section-title {
  color: #67c23a;
}

.failed-section .section-title {
  color: #f56c6c;
}

.card-result-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.card-result-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.card-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.card-thumbnail {
  width: 60px;
  height: 40px;
  object-fit: cover;
  border-radius: 2px;
  margin-right: 10px;
}

.card-id {
  font-size: 12px;
  color: #909399;
}

.card-message {
  flex: 2;
  display: flex;
  align-items: center;
}

.card-message i {
  margin-right: 8px;
}

.success-section .card-message i {
  color: #67c23a;
}

.failed-section .card-message i {
  color: #f56c6c;
}

.card-actions {
  flex: 1;
  text-align: right;
}

.retry-checkbox {
  margin-left: 15px;
}
/* 上传按钮禁用状态 */
.upload-button.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: #a0cfff;
}

/* 加载图标 */
.loading-icon {
  margin-left: 8px;
  animation: rotating 2s linear infinite;
}

/* 上传进度条样式 */
.upload-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 5px;
  background-color: rgba(0, 0, 0, 0.5);
}

.progress-text {
  color: white;
  font-size: 12px;
  text-align: center;
  margin-top: 2px;
}

/* 上传中的卡片样式 */
.card-item.uploading {
  position: relative;
}

.card-item.uploading::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 1;
}

/* 禁用删除按钮 */
.delete-card.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  color: #c0c4cc !important;
}

/* 上传遮罩样式 */
.uploading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.uploading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  padding: 30px 40px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.loading-icon {
  font-size: 40px;
  color: #409eff;
  margin-bottom: 15px;
  animation: rotating 2s linear infinite;
}

.uploading-text {
  font-size: 16px;
  color: #606266;
}

/* 旋转动画 */
@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>