import request from '@/utils/request'

// 获取工作时间记录详情
export function getWorkTimeRecord(date, scheduleId) {
  return request({
    url: '/workflow/attendance/get-work-time-record',
    method: 'get',
    params: {
      date,
      scheduleId
    }
  })
}

// 创建或更新签到记录
export function createOrUpdateCheckIn(data) {
  return request({
    url: '/workflow/attendance/check-in',
    method: 'post',
    data: data
  })
}

// 创建或更新签走记录
export function createOrUpdateCheckOut(data) {
  return request({
    url: '/workflow/attendance/check-out',
    method: 'post',
    data: data
  })
}

// 确认工作时数
export function confirmWorkHours(data) {
  return request({
    url: '/workflow/attendance/confirm-work-hours',
    method: 'post',
    data: data
  })
}

// PT同工确认工作时数
export function ptConfirmWorkHours(data) {
  return request({
    url: '/workflow/attendance/pt-confirm-work-hours',
    method: 'post',
    data: data
  })
}

// 获取编更记录
export function getScheduleRecord(date) {
  return request({
    url: '/workflow/attendance/get-schedule-record',
    method: 'get',
    params: {
      date
    }
  })
}

// 修改签到/签走记录
export function updateAttendanceRecord(data) {
  return request({
    url: '/workflow/attendance/update-record',
    method: 'put',
    data: data
  })
}

// 获取工作时数计算结果
export function calculateWorkHours(data) {
  return request({
    url: '/workflow/attendance/calculate-work-hours',
    method: 'post',
    data: data
  })
}
