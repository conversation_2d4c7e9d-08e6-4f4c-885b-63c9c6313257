<template>
  <div class="settings-container">
    <!-- 左侧二级菜单 -->
    <div
      v-if="!$isMobile() || ($isMobile() && currentSetup === 1)"
      class="left-menu"
    >
      <div class="menu-title">{{ $t("Setting") }}</div>
      

      <!-- 动态渲染有权限的菜单 -->
      <div
        v-for="menu in accessibleMenus"
        :key="menu.key"
        class="menu-item"
        :class="{ active: activeMenu === menu.key }"
        @click="changeMenu(menu.key)"
      >
        <img :src="getIcon(menu.key)" class="menu-icon" />
        {{ $t(menu.label) }}
      </div>

      
    </div>

    <!-- 右侧内容 -->
    <div
      v-if="!$isMobile() || ($isMobile() && currentSetup === 2)"
      class="right-content"
    >
      <div class="content-title">
        <img
          src="@/assets/pcm/card-detail/back.png"
          alt="返回箭头"
          class="back-arrow hidden-sm-and-up"
          @click="goBack()"
        />
        <span>{{ activeMenuLabel }}</span>
      </div>
      <div class="content-divider"></div>
      <div class="content-body">
        <MenuList v-if="activeMenu === 'permission'" />
        <DeptList v-if="activeMenu === 'department'" />
        <RoleList v-if="activeMenu === 'role'" />
        <UserList v-if="activeMenu === 'employee'" />
        <FileConfig v-if="activeMenu === 'file-config'" />
        <FileLog v-if="activeMenu === 'file-log'" />
        <ApiSuccessLog v-if="activeMenu === 'api-success-log'" />
        <ApiErrorLog v-if="activeMenu === 'api-error-log'" />
        <DictType
          v-if="activeMenu === 'dict-type'"
          @toDictData="handleDictDataPage"
        />
        <DcitData
          v-show="activeMenu === 'dict-data'"
          ref="dictData"
          v-has-permi="['system:dict:query']"
        />
      </div>
    </div>
  </div>
</template>

<script>
import UserList from "@/views/pcm/setting/user";
import RoleList from "@/views/pcm/setting/role";
import DeptList from "@/views/pcm/setting/dept";
import MenuList from "@/views/pcm/setting/menu";
import FileLog from "@/views/pcm/setting/file/index.vue";
import FileConfig from "@/views/pcm/setting/fileConfig/index.vue";
import ApiSuccessLog from "@/views/pcm/setting/apiAccessLog";
import ApiErrorLog from "@/views/pcm/setting/apiErrorLog";
import DictType from "@/views/pcm/setting/dict/index.vue";
import DcitData from "@/views/pcm/setting/dict/data.vue";

export default {
  name: "SettingsIndex",
  components: {
    UserList,
    RoleList,
    DeptList,
    MenuList,
    ApiSuccessLog,
    ApiErrorLog,
    DictType,
    DcitData,
    FileLog,
    FileConfig,
  },
  data() {
    return {
      activeMenu: "",
      currentSetup: 1,
      menuItems: [
        {
          key: "permission",
          permission: ["system:menu:query"],
          label: "permissionManagement",
          component: "MenuList",
        },
        {
          key: "role",
          permission: ["system:role:query"],
          label: "roleManagement",
          component: "RoleList",
        },
        {
          key: "department",
          permission: ["system:dept:query"],
          label: "departmentManagement",
          component: "DeptList",
        },
        {
          key: "employee",
          permission: ["system:user:list"],
          label: "staffManagement",
          component: "UserList",
        },
        {
          key: "file-config",
          permission: ["infra:file-config:query"],
          label: "fileConfig",
          component: "FileConfig",
        },
        {
          key: "file-log",
          permission: ["infra:file:query"],
          label: "fileRecord",
          component: "FileLog",
        },
        {
          key: "api-success-log",
          permission: ["infra:api-access-log:query"],
          label: "apiSuccessLogs",
          component: "ApiSuccessLog",
        },
        {
          key: "api-error-log",
          permission: ["infra:api-error-log:query"],
          label: "apiErrorLogs",
          component: "ApiErrorLog",
        },
        {
          key: "dict-type",
          permission: ["system:dict:query"],
          label: "dictionaryType",
          component: "DictType",
        },
        {
          key: "dict-data",
          permission: ["system:dict:query"],
          label: "dictionaryData",
          component: "DcitData",
        },
      ],
    };
  },
  computed: {
    activeMenuLabel() {
      const menu = this.menuItems.find((item) => item.key === this.activeMenu);
      return menu ? this.$t(menu.label) : "";
    },
    accessibleMenus() {
      // 1. 如果是admin账号，直接返回所有菜单
      if (this.$store.getters.roles?.includes("super_admin")) {
        return this.menuItems;
      }

      // 2. 普通账号按权限过滤
      return this.menuItems.filter((item) => {
        // 如果菜单不需要权限，则直接显示
        if (!item.permission || item.permission.length === 0) return true;

        // 检查是否有任一权限
        return item.permission.some((perm) => this.$auth.hasPermi(perm));
      });
    },
  },
  created() {
    this.setDefaultActiveMenu();
  },
  methods: {
    setDefaultActiveMenu() {
      if (this.accessibleMenus.length > 0) {
        // 尝试找到上次选择的菜单（如果仍然有权限）
        const lastSelected = localStorage.getItem("lastSelectedMenu");
        const validLastSelection =
          lastSelected &&
          this.accessibleMenus.some((m) => m.key === lastSelected);

        // 优先使用上次选择，否则选择第一个非dict-data菜单
        this.activeMenu = validLastSelection
          ? lastSelected
          : this.accessibleMenus.find((m) => m.key !== "dict-data")?.key ||
            this.accessibleMenus[0]?.key;
      } else {
        this.handleNoPermission();
      }
    },

    handleNoPermission() {
      this.$modal.msgWarning(this.$t("No permission to access any settings"));
      this.$router.push("/");
    },

    goBack() {
      this.currentSetup = 1;
    },

    changeMenu(menu) {
      // 保存上次选择的菜单
      localStorage.setItem("lastSelectedMenu", menu);

      const hasPermission = this.accessibleMenus.some(
        (item) => item.key === menu
      );
      if (hasPermission) {
        this.activeMenu = menu;
        if (this.$isMobile()) {
          this.currentSetup = 2;
        }
      } else {
        this.$modal.msgError(this.$t("No permission to access this menu"));
      }
    },

    handleDictDataPage(dictType) {
      if (
        this.$refs.dictData &&
        this.accessibleMenus.some((item) => item.key === "dict-data")
      ) {
        this.$refs.dictData.queryParams.dictType = dictType;
        this.$refs.dictData.getList();
        this.activeMenu = "dict-data";
      } else {
        this.$modal.msgError(
          this.$t("No permission to access dictionary data")
        );
      }
    },

    getIcon(menu) {
      const icons = {
        permission: require("@/assets/pcm/setting/icon-permission.png"),
        role: require("@/assets/pcm/setting/icon-role.png"),
        department: require("@/assets/pcm/setting/icon-department.png"),
        "employee": require("@/assets/pcm/setting/icon-employee.png"),
        "file-config": require("@/assets/pcm/setting/icon-file-config.png"),
        "file-log": require("@/assets/pcm/setting/icon-file-log.png"),
        "api-success-log": require("@/assets/pcm/setting/icon-success-log.png"),
        "api-error-log": require("@/assets/pcm/setting/icon-error-log.png"),
        "dict-type": require("@/assets/pcm/setting/icon-dict-type.png"),
        "dict-data": require("@/assets/pcm/setting/icon-dict-data.png"),
      };
      return icons[menu] || "";
    },
  },
};
</script>

<style scoped>
.settings-container {
  display: flex;
  height: 100%;
  background-color: #f0f4fa;
}

.left-menu {
  flex: 2;
  background-color: white;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.menu-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 10px;
  font-size: 14px;
  color: #1f1f1f;
  font-weight: 400;
}

.menu-item:hover {
  background-color: #ed6c001a;
  color: #ed6c00;
}

.menu-item.active {
  background-color: #ed6c001a;
  color: #ed6c00;
}

.menu-icon {
  width: 16px;
  height: 16px;
  margin-right: 10px;
  /* 确保默认状态下可见 */
  opacity: 1;
  transition: all 0.3s ease;
}

.menu-item:hover .menu-icon,
.menu-item.active .menu-icon {
  filter: brightness(0) saturate(100%) invert(50%) sepia(100%) saturate(1000%)
    hue-rotate(0deg) brightness(100%) contrast(100%);
}

.menu-divider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 20px 0;
}

.right-content {
  margin-left: 10px;
  background-color: white;
  padding: 20px;
  height: 100%;
  flex: 9;
  overflow-y: auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.back-arrow {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  margin-right: 8px;
  cursor: pointer;
}

@media screen and (max-width: 768px) {
  .right-content {
    margin-left: 0;
  }
  .content-title {
    text-align: center;
    position: relative;
  }
}

.content-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}

.content-divider {
  height: 1px;
  background-color: #e0e0e0;
  margin-bottom: 20px;
}
</style>