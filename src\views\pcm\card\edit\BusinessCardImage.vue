<template>
  <div class="card-image-container">
    <div class="dual-image-preview">
      <!-- 正面图片 -->
      <div class="image-wrapper" :class="{ 'has-image': frontImage }">
        <div
          v-if="frontImage"
          class="image-content"
          @click="showPreview(frontImage)"
        >
          <img
            :src="
              frontImage === '*'
                ? require('@/assets/pcm/card-detail/access_denied.jpg')
                : frontImage
            "
            :alt="$t('businessCard.frontSide')"
            class="card-image"
          />
          <div class="image-overlay">
            <span class="image-text">{{ $t("businessCard.frontSide") }}</span>
            <div class="image-actions">
              <i
                class="el-icon-delete"
                @click.stop="removeImage('front')"
                :title="$t('businessCard.deleteFront')"
              ></i>
              <i
                class="el-icon-zoom-in"
                @click.stop="showPreview(frontImage)"
                :title="$t('businessCard.preview')"
              ></i>
            </div>
          </div>
        </div>
        <div v-else class="upload-area" @click="triggerUpload('front')">
          <i class="el-icon-plus"></i>
          <p>{{ $t("businessCard.uploadFront") }}</p>
          <p class="upload-hint">{{ $t("businessCard.uploadHint") }}</p>
        </div>
        <!-- 上传 Loading 效果 -->
        <div v-if="loading && currentSide === 'front'" class="upload-loading">
          <el-progress
            type="circle"
            :percentage="uploadProgress"
            :width="60"
            :stroke-width="4"
            :show-text="false"
          ></el-progress>
          <span class="loading-text">{{ $t("businessCard.uploading") }}</span>
        </div>
      </div>

      <!-- 反面图片 -->
      <div class="image-wrapper" :class="{ 'has-image': backImage }">
        <div
          v-if="backImage"
          class="image-content"
          @click="showPreview(backImage)"
        >
          <img
            :src="
              backImage === '*'
                ? require('@/assets/pcm/card-detail/access_denied.jpg')
                : backImage
            "
            :alt="$t('businessCard.backSide')"
            class="card-image"
          />
          <div class="image-overlay">
            <span class="image-text">{{ $t("businessCard.backSide") }}</span>
            <div class="image-actions">
              <i
                class="el-icon-delete"
                @click.stop="removeImage('back')"
                :title="$t('businessCard.deleteBack')"
              ></i>
              <i
                class="el-icon-zoom-in"
                @click.stop="showPreview(backImage)"
                :title="$t('businessCard.preview')"
              ></i>
            </div>
          </div>
        </div>
        <div v-else class="upload-area" @click="triggerUpload('back')">
          <i class="el-icon-plus"></i>
          <p>{{ $t("businessCard.uploadBack") }}</p>
          <p class="upload-hint">{{ $t("businessCard.uploadHint") }}</p>
        </div>
        <!-- 上传 Loading 效果 -->
        <div v-if="loading && currentSide === 'back'" class="upload-loading">
          <el-progress
            type="circle"
            :percentage="uploadProgress"
            :width="60"
            :stroke-width="4"
            :show-text="false"
          ></el-progress>
          <span class="loading-text">{{ $t("businessCard.uploading") }}</span>
        </div>
      </div>
    </div>

    <!-- 隐藏的上传控件 -->
    <el-upload
      v-show="false"
      action=""
      :http-request="handleUpload"
      :before-upload="beforeUpload"
      accept="image/*"
      :show-file-list="false"
      ref="uploader"
    >
      <template #trigger>
        <el-button v-show="false">{{ $t("businessCard.upload") }}</el-button>
      </template>
    </el-upload>

    <!-- 图片预览对话框 -->
    <el-dialog
      :visible.sync="previewVisible"
      :title="$t('businessCard.previewTitle')"
      width="80%"
      top="5vh"
      custom-class="image-preview-dialog"
      @close="previewVisible = false"
    >
      <div class="preview-content">
        <img
          :src="previewImage"
          :alt="$t('businessCard.previewTitle')"
          class="preview-image"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { carUpload } from "@/api/pcm/card";
export default {
  name: "BusinessCardImage",
  props: {
    frontImage: String,
    backImage: String,
  },
  emits: ["update:frontImage", "update:backImage"],
  data() {
    return {
      previewVisible: false,
      previewImage: "",
      currentSide: "front",
      loading: false,
      uploadProgress: 0,
      progressInterval: null,
    };
  },
  methods: {
    // 触发上传
    triggerUpload(side) {
      this.currentSide = side;
      this.$nextTick(() => {
        this.$refs.uploader.$el.querySelector("input").click();
      });
    },
    // 上传前校验
    beforeUpload(file) {
      const isImage = file.type.startsWith("image/");
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isImage) {
        this.$message.error(this.$t("businessCard.imageTypeError"));
        return false;
      }
      if (!isLt5M) {
        this.$message.error(this.$t("businessCard.sizeLimitError"));
        return false;
      }

      // 开始上传前初始化进度
      this.loading = true;
      this.uploadProgress = 0;

      // 模拟进度更新（实际项目中可以根据上传事件更新）
      this.progressInterval = setInterval(() => {
        if (this.uploadProgress < 90) {
          this.uploadProgress += 10;
        }
      }, 300);
      return true;
    },

    // 处理上传
    async handleUpload({ file }) {
      try {
        const formData = new FormData();
        formData.append("files", file);

        // 先创建临时 URL 用于预览
        const tempUrl = URL.createObjectURL(file);
        this.$emit(`update:${this.currentSide}Image`, tempUrl);

        // 然后上传到服务器
        const res = await carUpload(formData);
        this.uploadProgress = 100; // 上传完成

        // 更新为服务器返回的 URL
        const imageUrl = res.data[0].url;
        this.$emit(`update:${this.currentSide}Image`, imageUrl);
        this.$message.success(this.$t("businessCard.uploadSuccess"));
      } catch (e) {
        this.$message.error(this.$t("businessCard.uploadFailed"));
        // 上传失败时清除临时 URL
        this.$emit(`update:${this.currentSide}Image`, "");
      } finally {
        clearInterval(this.progressInterval);
        this.loading = false;
        this.uploadProgress = 0;
      }
    },

    // 删除图片
    removeImage(side) {
      this.$confirm(
        this.$t("businessCard.deleteConfirm"),
        this.$t("businessCard.tip"),
        {
          confirmButtonText: this.$t("businessCard.confirm"),
          cancelButtonText: this.$t("businessCard.cancel"),
          type: "warning",
        }
      ).then(() => {
        this.$emit(`update:${side}Image`, "");
        this.$message.success(this.$t("businessCard.deleted"));
      });
    },

    // 显示大图预览
    showPreview(imageUrl) {
      if (!imageUrl || imageUrl === "*") {
        this.previewImage = require("@/assets/pcm/card-detail/access_denied.jpg");
      } else {
        this.previewImage = imageUrl;
      }
      this.previewVisible = true;
    },
  },
};
</script>

<style scoped>
.card-image-container {
  width: 100%;
}

.dual-image-preview {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.image-wrapper {
  flex: 1;
  min-height: 220px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  background-color: #f5f7fa;
  transition: all 0.3s ease;
}

.image-wrapper.has-image {
  background-color: transparent;
}

.image-content {
  position: relative;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-content:hover .image-overlay {
  opacity: 1;
}

.image-text {
  font-size: 14px;
}

.image-actions i {
  color: white;
  font-size: 18px;
  margin-left: 12px;
  cursor: pointer;
  transition: color 0.2s;
}

.image-actions i:hover {
  color: #409eff;
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  cursor: pointer;
  padding: 20px;
  transition: all 0.3s;
}

.upload-area:hover {
  color: #409eff;
  background-color: #f0f7ff;
}

.upload-area i {
  font-size: 36px;
  margin-bottom: 10px;
}

.upload-area p {
  margin: 0;
  font-size: 14px;
}

.upload-hint {
  font-size: 12px;
  color: #c0c4cc;
  margin-top: 5px !important;
}

.preview-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 70vh;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* 新增上传 Loading 样式 */
.upload-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-text {
  margin-top: 10px;
  font-size: 14px;
  color: #606266;
}

@media (max-width: 768px) {
  .dual-image-preview {
    flex-direction: column;
  }

  .image-wrapper {
    min-height: 180px;
  }

  .upload-area i {
    font-size: 28px;
  }

  .upload-area p {
    font-size: 13px;
  }

  .upload-hint {
    font-size: 11px;
  }
  .upload-loading {
    background: rgba(255, 255, 255, 0.9);
  }

  .loading-text {
    font-size: 12px;
  }
}
</style>

<style>
.image-preview-dialog .el-dialog__body {
  padding: 0;
}
</style>