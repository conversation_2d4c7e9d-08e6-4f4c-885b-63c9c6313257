import request from '@/utils/request'

// 创建标签表，存储名片相关的标签
export function creatTag(data) {
  return request({
    url: '/pcm/tags/create',
    method: 'post',
    data: data
  })
}

// 更新标签表，存储名片相关的标签
export function updatTag(data) {
  return request({
    url: '/pcm/tags/update',
    method: 'put',
    data: data
  })
}

// 删除标签表，存储名片相关的标签
export function deletTag(ids) {
  return request({
    url: '/pcm/tags/deleteTagsByIds',
    method: 'post',
    data:{ ids: ids}
  })
}

// 获得标签表，存储名片相关的标签
export function getTag(id) {
  return request({
    url: '/pcm/tags/get?id=' + id,
    method: 'get'
  })
}

// 获得标签表，存储名片相关的标签分页
export function page(params) {
  return request({
    url: '/pcm/tags/page',
    method: 'get',
    params
  })
}

// 获得标签表，存储名片相关的标签不分页
export function getTagsAll(params) {
  return request({
    url: '/pcm/tags/getTagsAll',
    method: 'get',
    params
  })
}
// 导出标签表，存储名片相关的标签 Excel
export function exportTagExcel(params) {
  return request({
    url: '/pcm/tags/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
