<template>
  <div class="dashboard-container">
    <!-- TopBar -->
    <TopBar :user="user" />

    <!-- 主内容区域 -->
    <div class="main-content">
      <MenuBar class="hidden-xs-only"/>
      <MainContain />
    </div>
  </div>
</template>

<script>
import TopBar from "@/layout/components/pcm/TopBar.vue";
import MenuBar from "@/layout/components/pcm/MenuBar.vue";
import MainContain from "@/layout/components/pcm/MainContain.vue";

export default {
  name: "Dashboard",
  components: {
    TopBar,
    MenuBar,
    MainContain
  },
  computed: {
    user() {
      return this.$store.state.user; // 假设从 Vuex 中获取用户信息
    }
  }
};
</script>

<style scoped>
.dashboard-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
   background-color: #f0f4fa;
}

.main-content {
  display: flex;
  flex: 1;
   overflow-y: auto; /* 启用滚动条 */
}
</style>