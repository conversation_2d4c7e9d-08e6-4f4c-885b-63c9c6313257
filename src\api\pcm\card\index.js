import request from '@/utils/request'

// 创建卡片
export function createCard(data) {
  return request({
    url: '/pcm/card/create',
    method: 'post',
    data: data
  })
}

// 更新卡片
export function updateCard(data) {
  return request({
    url: '/pcm/card/update',
    method: 'put',
    data: data
  })
}

//保存并编辑下一张
export function saveAndReturnPrevious(data) {
  return request({
    url: '/pcm/card/saveAndReturnPrevious',
    method: 'put',
    data: data
  })
}


// 删除卡片
export function deleteCard(ids) {
  return request({
    url: '/pcm/card/delete',
    method: 'delete',
    data:{ids:ids}
  })
}

// 获得卡片
export function getCard(id) {
  return request({
    url: '/pcm/card/get?id=' + id,
    method: 'get'
  })
}


// 统计创建者名片数
export function countCardsByCreatorInDept(companyId) {
  return request({
    url: '/pcm/card/countCardsByCreatorInDept?companyId=' + companyId,
    method: 'get'
  })
}
//上传图片
export function carUpload(formData){
  return request({
    url:'/pcm/ocr/upload',
    method:'post',
    data:formData
  })
}

//ocr识别图片
export function recognize(fileIds){
  return request({
    url:'/pcm/ocr/recognize',
    method:'post',
    data:{fileIds:fileIds}
  })
}

//ocr雙面識別
export function doubleSidedCard(data){
  return request({
    url:'/pcm/ocr/doubleSidedCard',
    method:'post',
    data:data
  })
}

// 获得卡片分页
export function getCardPage(params) {
  return request({
    url: '/pcm/card/page',
    method: 'post',
    data:params
  })
}

// 获得卡片分页【根据groupId分组】
export function getCardDistinctPage(params) {
  return request({
    url: '/pcm/card/getDistinctByGroupIdPage',
    method: 'post',
    data:params
  })
}

// 获得所有卡片id【根据groupId分组】
export function getDistinctIds(params){
  return request({
    url:'/pcm/card/getDistrictIds',
     method: 'post',
    data:params
  })
}


// 批量设置标签、名片创建者
export function batchUpdateField(data) {
  return request({
    url: '/pcm/card/batchUpdateField',
    method: 'put',
    data:data
  })
}

// 导出卡片 Excel
export function exportCardExcel(data) {
  return request({
    url: '/pcm/card/export-excel',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}

// 合并名片
export function merge(ids) {
  return request({
    url: '/pcm/card/merge',
    method: 'post',
    data:{ ids: ids}
  })
}

//名片回收站列表
export function getCardTrashPage(params) {
  return request({
    url: '/pcm/cardtrash/getCardTrash',
    method: 'get',
    data:params
  })
}



// 批量恢复名片
export function restore(ids) {
  return request({
    url: '/pcm/cardtrash/restore',
    method: 'put',
    data:{ ids: ids}
  })
}



// 批量彻底删除名片
export function permanentlyDelete(ids) {
  return request({
    url: '/pcm/cardtrash/permanentlyDelete',
    method: 'delete',
    data:{ ids: ids}
  })
}


//根据用户ID统计名片数
export function countCardByUserId(id) {
  return request({
    url: '/pcm/card/countCardByUserId?userId=' + id,
    method: 'get'
  })
}

//根据groupId获取分组信息
export function getCardsByGroudId(groupId){
  return request({
    url: '/pcm/card/getCardsByGroudId?groupId=' + groupId,
    method: 'get'
  })
}

//下载名片模版
export function importTemplate() {
  return request({
    url: '/pcm/card/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}

//获取名片所有字段
export function getCardFields() {
  return request({
    url: '/pcm/card/getCardFieldNames',
    method: 'get'
  })
}

//根据名片id获取用户权限
export function getCardPermissions(cardId) {
  return request({
    url: '/pcm/permission/getCardPermissionByCardId?cardId=' + cardId,
    method: 'get'
  })
}

//保存名片的用户字段权限
export function savePermission(cardId,data) {
  return request({
    url: '/pcm/permission/savePermission?cardId=' + cardId,
    method: 'post',
    data:data
  })
}

/**
 * 根据公司查询所包含的名片列表
 * @param {*} data 
 * @returns 
 */
export function getCardPageByCompanyId(data){
  return request({
    url:'/pcm/card/getCardPageByCompanyId',
    method:'post',
    data:data
  })
}




