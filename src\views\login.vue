<template>
  <div class="login-container">
    <!-- 未登录状态 -->
    <!-- SSO Loading状态 -->
    <div v-if="ssoLoading" class="sso-loading">
      <div class="sso-loading-content">
        <div class="sso-spinner"></div>
        <p>{{ $t("ssoLoggingIn") }}</p>
      </div>
    </div>

    <!-- Login Form Container -->
    <div v-if="!hasAccessToken" class="login-form-container">
      <div class="login-form">
        <!-- Logo -->
        <div class="logo-box">
          <img
            src="@/assets/logo/login-logo2.png"
            alt="Logo"
            class="login-logo"
          />
        </div>

        <!-- Title -->
        <h2 class="login-title">{{ $t("loginToHKYouthAssociation") }}</h2>

        <!-- Email/Phone Input -->
        <el-form
          ref="loginForm"
          :model="loginForm"
          :rules="LoginRules"
          class="login-form"
        >
          <el-button
            :loading="ssoBtnLoading"
            class="sso-button"
            @click="handleSsoLogin"
          >
            Intranet SSO
          </el-button>

          <div class="divider-flex">
            <div class="divider-line"></div>
            <span class="divider-text">{{ $t("or") }}</span>
            <div class="divider-line"></div>
          </div>

          <el-form-item prop="username">
            <div class="input-group">
              <label for="email-phone">{{ $t("emailOrPhone") }}</label>
              <input
                type="text"
                id="email-phone"
                class="input-field"
                v-model="loginForm.username"
                @input="$refs.loginForm.validateField('username')"
                @keyup.enter="login"
              />
            </div>
          </el-form-item>

          <!-- Password Input -->
          <el-form-item prop="password">
            <div class="input-group">
              <label for="password">{{ $t("password") }}</label>
              <input
                type="password"
                id="password"
                class="input-field"
                v-model="loginForm.password"
                @input="$refs.loginForm.validateField('password')"
                @keyup.enter="login"
              />
            </div>
          </el-form-item>

          <!-- Operations -->
          <div class="operations">
            <div class="remember-me">
              <input
                type="checkbox"
                id="remember-me"
                v-model="loginForm.rememberMe"
              />
              <label for="remember-me">{{ $t("rememberMe") }}</label>
            </div>
            <a href="#" class="forgot-password">{{ $t("forgotPassword") }}？</a>
          </div>

          <!-- Login Button -->
          <el-button
            :loading="loading"
            class="login-button"
            native-type="submit"
            @click="login"
            >{{ $t("continue") }}</el-button
          >
        </el-form>
      </div>
    </div>
  </div>
</template>
<script>
import TopBar from "@/layout/components/pcm/TopBar.vue";
import { getSlientLogin } from "@/api/login";
import {
  getPassword,
  getRememberMe,
  getUsername,
  removePassword,
  removeRememberMe,
  removeUsername,
  setPassword,
  setRememberMe,
  setUsername,
} from "@/utils/auth";
export default {
  name: "Login",
  components: {
    TopBar,
  },
  created() {
    // 获取回调的code或accessToken
    const accessToken = this.$route.query.accessToken;
    if (accessToken) {
      this.hasAccessToken = true;
      this.ssoBtnLoading = true;
      this.loginBySsoAccessToken(accessToken);
    }
    // 重定向地址
    this.redirect = this.$route.query.redirect
      ? decodeURIComponent(this.$route.query.redirect)
      : undefined;
    this.getCookie();
  },
  data() {
    return {
      loading: false,
      ssoLoading: false,
      ssoBtnLoading: false,
      hasAccessToken: false, // 新增标志位
      redirect: undefined,
      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
      },
      LoginRules: {
        username: [
          { required: true, trigger: "change", message: "用户名不能为空" },
        ],
        password: [
          { required: true, trigger: "change", message: "密码不能为空" },
        ],
      },
    };
  },
  methods: {
    getCookie() {
      const username = getUsername();
      const password = getPassword();
      const rememberMe = getRememberMe();
      this.loginForm = {
        ...this.loginForm,
        username: username ? username : this.loginForm.username,
        password: password ? password : this.loginForm.password,
        rememberMe: rememberMe ? getRememberMe() : false,
      };
    },
    login() {
      // 模拟登录逻辑
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          // 设置 Cookie
          if (this.loginForm.rememberMe) {
            setUsername(this.loginForm.username);
            setPassword(this.loginForm.password);
            setRememberMe(this.loginForm.rememberMe);
          } else {
            removeUsername();
            removePassword();
            removeRememberMe();
          }
          // 发起登陆
          console.log("发起登录", this.loginForm);
          this.$store
            .dispatch("Login", this.loginForm)
            .then(() => {
              this.$router.push({ path: this.redirect || "/" }).catch(() => {});
            })
            .catch(() => {
              this.loading = false;
            });
        }
      });
    },
    async handleSsoLogin() {
      // 进行跳
      getSlientLogin().then((res) => {
        this.ssoBtnLoading = true;
        window.location.href = res.data;
      });
    },

    /**
     * 通过sso返回的accessToken 进行系统登录
     */
    async loginBySsoAccessToken(accessToken) {
      this.ssoLoading = true;
      this.ssoBtnLoading = true;
      this.$store
        .dispatch("SsoLogin", accessToken)
        .then(() => {
          this.$router.push({ path: this.redirect || "/" }).catch(() => {});
        })
        .catch(() => {
          console.log("sso登錄失敗！此處跳轉至Access Denied Page");
          this.$router
            .push({ path: this.redirect || "/access-denied" })
            .catch(() => {});
          this.ssoBtnLoading = false;
          this.ssoLoading = false;
        });
    },
  },
};
</script>
<style scoped>
::v-deep .el-form-item {
  margin-bottom: 0;
}
/* Login Container */
.login-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f0f4fa;
}

/* Login Form Container */
.login-form-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 10px;
  background-color: #ffffff;
  border-radius: 10px;
}

.login-form {
  background-color: #ffffff;
  border-radius: 10px;
  padding: 20px;
  /* box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); */
  width: 100%;
  max-width: 460px;
}

.logo-box {
  height: 103px;
}

.login-logo {
  object-fit: contain;
  object-position: center;
  margin-bottom: 20px;
  width: 100%;
  height: 100%;
}

.login-title {
  text-align: center;
  color: #000000;
  font-size: 29px;
  font-weight: 500;
  margin-bottom: 20px;
}

.input-group {
  margin-bottom: 10px;
}

.input-group label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #000000;
  font-weight: 400;
}

.input-field {
  width: 100%;
  max-width: 400px;
  height: 44px;
  background-color: #f5f5f5;
  padding: 0 10px;
  border: 0px solid #cccccc00;
  border-radius: 4px;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 400;
}
.input-field:focus {
  outline: none; /* 移除聚焦时的默认轮廓 */
}

.operations {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.remember-me label {
  font-size: 14px;
  color: #1f1f1f;
}

.forgot-password {
  color: #1f1f1f;
  text-decoration: none;
  font-size: 14px;
}

.login-button,
.sso-button {
  width: 100%;
  max-width: 400px;
  height: 44px;
  background-color: #ffffff;
  color: #1f1f1f;
  border: 1px solid #0000001a;
  border-radius: 22px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}
.sso-button {
  background-color: #1ea235;
  color: #ffffff;
  border: none;
}

.sso-button:hover {
  background-color: #188a2b;
}

.login-button:hover {
  background-color: #1f1f1f;
  color: #ffffff;
}
.divider-flex {
  display: flex;
  align-items: center;
  margin: 20px 0;
}

.divider-line {
  flex: 1;
  height: 0.2px;
  background-color: #f1f1f1; /* 线的颜色 */
}

.divider-text {
  padding: 0 10px; /* 调整文字与线的间距 */
  color: #999999; /* 文字颜色 */
}

.sso-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
}

.sso-loading-content {
  text-align: center;
}

.sso-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1ea235;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>