export default {
  language: '简体中文', // 语言切换按钮的文本
  ssoLoggingIn: '正在通过SSO登录...',
  systemPrompt: '系统提示',
  confirmLogout: '确定注销并退出系统吗',
  loginToHKYouthAssociation: '登录到香港青年协会',
  emailOrPhone: '电子邮件/电话号码',
  password: '密码',
  rememberMe: '记住我',
  forgotPassword: '忘记密码',
  continue: '继续',
  or: '或',

  //左边菜单栏
  MyCalendar: "轮值表/签到记录",
  WorkflowProfile: "個人資料",
  Card: "名片库",
  Company: "公司",
  Meeting: "商谈记录",
  Colleague: "同事",
  Setting: "设置",
  menu: '功能表',
  //顶部
  personalInfo: '个人信息',
  logout: '退出系统',

  titleDefault: '标题',
  allCards: '所有名片',

  advancedFilter: '高级筛选',
  clearFilter:'清除筛选',
  uploadCard: '上传名片',
  tag: '标签',
  shareCard: '共享名片',
  delete: '删除',
  modifyCreator: '修改名片创建者',
  exportExcel: '导出Excel',
  mergeCards: '合并名片',
  name: '姓名',
  cardImage: '名片图像',
  companyPosition: '公司/职位',
  contactInfo: '联系方式',
  creator: '创建者',
  cardCreator: '名片创建者',
  action: '操作',
  selectTag: '选择标签',
  companyTagManagement: '公司标签管理',
  uploadCardImage: '上传名片图片',
  cancel: '取消',
  confirm: '确定',

  total: '共',
  businessCardsUnit: '张名片',
  action: '操作',
  businessCardType: '类型',
  convertEmailSignature: '如何将邮件签名转换为名片',
  learnMore: '了解详情',
  sortByDate: '按时间排序',
  sortByName: '按姓名排序',
  sortByCompany: '按公司排序',
  continueUploading: '继续上传',
  excelImport: "Excel批量导入",
  manualInput: "手动输入",

  cardInformation: '名片信息',
  by: '由',
  add_: '添加',
  addToCompanyCardFolder: '添加到公司名片夹',
  manualVerification: '人工校验',
  manuallyEdited: '手动手动编辑过',
  editTags: '编辑标签',
  searchHint: '可根据姓名、公司、职位、邮箱、手机号等搜索',
  advancedFilter: '高级筛选',
  creationTimeFilter: '创建时间筛选',
  cardCreator: '名片创建人',
  tags: '标签',
  validationStatus: '精准校验状态',
  showMoreKeywords: '显示更多关键字',
  to: '至',
  selectCreator: '请选择创建人',
  hideKeywords: '隐藏关键字',
  enterMoreKeywords: "您可以通过插入';'(分号)来指定多个搜索词",
  allCompanies: '所有公司',
  enterCompanyName: '请输入公司名称',
  organization: '组织结构',
  businessCards: '名片列表',
  negotiationRecords: '商谈记录',
  addNegotiationRecord: '商谈记录',
  colleaguesWithViewPermission: '有权限查看的同事',
  departmentsWithViewPermission: '有权查看的部门',
  viewPermissionNote: '创建者的上司、管理员均有权查看该名片',
  all: '全部',
  myNegotiations: '我的商谈记录',
  subordinateNegotiations: '下属的商谈记录',
  viewByDepartment: '按部门查看',
  searchNegotiations: '请输入关键字搜索商谈记录',
  colleague: '同事',
  profile: '个人资料',
  colleagueBusinessCard: '名片',
  edit: '编辑',
  myQRCode: '我的二维码',
  myECardLink: '我的电子名片链接',
  settings: '设置',
  permissionManagement: '权限管理',
  roleManagement: '角色管理',
  departmentManagement: '部门管理',
  staffManagement: '员工管理',
  emailAccount: '邮箱账号',
  emailTemplate: '邮箱模版',
  emailLogs: '邮箱日志',
  apiSuccessLogs: 'API成功日志',
  apiErrorLogs: 'API错误日志',
  dictionaryType: '字典类型',
  dictionaryData: '字典数据',
  menuName: '菜单名称',
  status: '状态',
  search: '搜索',
  reset: '重置',
  add: '新增',
  modify: '修改',
  delete: '删除',
  expandCollapse: '展开/折叠',
  icon: '图标',
  sort: '排序',
  permissionId: '权限标识',
  componentPath: '组件路径',
  componentName: '组件名称',
  operation: '操作',
  hideSearch: '隐藏搜索',
  refresh: '刷新',
  departmentName: '部门名称',
  manager: '负责人',
  creationTime: '创建时间',
  menuStatus: '菜单状态',
  parentMenu: '上级菜单',
  directory: '目录',
  button: '按钮',
  menuIcon: '菜单图标',
  displayOrder: '显示排序',
  routePath: '路由地址',
  isVisible: '是否显示',
  visible: '显示',
  hidden: '隐藏',
  alwaysShow: '总是显示',
  always: '总是',
  no: '不是',
  parentDepartment: '上级部门',
  departmentStatus: '部门状态',
  email: '邮箱',
  username: '用户名称',
  enterUsername: '请输入用户名称',
  phoneNumber: '手机号码',
  enterPhoneNumber: '请输入手机号码',
  userStatus: '用户状态',
  startTime: '开始时间',
  endTime: '结束时间',
  userId: '用户编号',
  nickname: '用户昵称',
  more: '更多',
  resetPassword: '重置密码',
  assignRole: '分配角色',
  affiliatedDepartment: '归属部门',
  gender: '用户性别',
  position: '职位',
  remark: '备注',
  enterEmail: '请输入邮箱',
  id: '编号',
  smtpDomain: 'SMPT服务器域名',
  smtpPort: 'SMPT服务器端口',
  enableSSL: '是否开启SSL',
  templateName: '模版名称',
  enterTemplateName: '请输入模版名称',
  templateCode: '模版编码',
  enterTemplateCode: '请输入模版编码',
  enterEmailAccount: '请输入邮箱账号',
  enableStatus: '开启状态',
  selectEnableStatus: '请选择开启状态',
  templateTitle: '模版标题',
  templateContent: '模版内容',
  senderName: '发送人名称',
  test: '测试',
  recipientEmail: '收件邮箱',
  parameters: '参数',
  enterRecipientEmail: '请输入接收邮箱',
  templateId: '模版编号',
  enterTemplateId: '请输入模版编号',
  sendStatus: '发送状态',
  selectSendStatus: '请选择发送状态',
  sendTime: '发送时间',
  userType: '用户类型',
  selectUserType: '请选择用户类型',
  requestUrl: '请求地址',
  enterRequestUrl: '请输入请求地址',
  resultCode: '结果码',
  enterResultCode: '请输入结果码',
  logId: '日志编号',
  requestMethod: '请求方法名',
  requestTime: '请求时间',
  executionDuration: '执行时长',
  operationResult: '操作结果',
  operationModule: '操作模块',
  operationName: '操作名',
  operationType: '操作类型',
  logKey: '日志主键',
  traceId: '链路追踪',
  appName: '应用名',
  userInfo: '用户信息',
  requestInfo: '请求信息',
  requestParams: '请求参数',
  exceptionTime: '异常时间',
  exceptionName: '异常名',
  handleStatus: '处理状态',
  details: '详细',
  processed: '已处理',
  processor: "处理人",
  processingTime: "处理时间",
  ignored: '已忽略',
  export: '导出',
  dictionaryName: '字典名称',
  enterDictionaryName: '请输入字典名称',
  enterDictionaryType: '请输入字典类型',
  selectDictionaryStatus: '请选择字典状态',
  dictionaryLabel: '字典标签',
  enterDictionaryLabel: '请输入字典标签',
  dictionaryCode: '字典编码',
  dictionaryKey: '字典键值',
  dictionarySort: '字典排序',
  colorType: '颜色类型',
  toolbox: '工具箱',
  exportToExcel: '导出到Excel',
  batchImportCards: 'Excel批量导入名片',
  changePassword: '修改密码',
  companyTagManagement: '公司标签管理',
  cardRecycleBin: '名片回收站',
  exportAllNotice: '所有名片将会导出为EXCEL文件',
  exportFilterNotice: '您也可到名片夹页面，使用高级筛选功能，仅导出符合定条件的名片',
  exportCompanyCards: '导出公司名片夹到Excel',
  step1: '步骤1',
  step2: '步骤2',
  step3: '步骤3',
  downloadTemplate: '下载标准模板',
  clickDownloadTemplate: '点击下载Excel模板',
  confirmTemplateFormat: '请确认您的文件数据格式与标准模板保持一致',
  learnMore: '了解更多',
  importExcelNotice: '导入您的EXCEL联系人文件',
  fileSizeLimit: '建议不超过10MB，不超过10000条联系人',
  selectExcelFile: '选择Excel文件上传',
  basicInfo: '基本资料',
  userEmail: '用户邮箱',
  department: '所属部门',
  belongRole: '所属角色',
  creationDate: '创建日期',
  oldPassword: '旧密码',
  newPassword: '新密码',
  confirmPassword: '确认密码',
  modifyAvatar: '修改头像',
  select: '选择',
  submit: '提交',
  jobPosition: '所属岗位',
  enterOldPassword: '请输入旧密码',
  enterNewPassword: '请输入新密码',
  confirmPassword: '请确认密码',
  save: '保存',
  close: '关闭',
  enterTagName: '请输入标签名称',
  permanentlyDelete: '彻底删除',
  restore: '恢复',
  deletedBy: '删除者',

  editCard: '编辑名片',
  addCard:  '添加名片',
  saveAndEditNext: '保存并编辑下一张',
  partnerInfo: '伙伴资讯',
  organizationInfo: '机构资讯',
  socialMedia: '社交媒体',
  type: '类型',
  content: '内容',
  enterContent: '请输入内容',
  uploadHint: '可上传5M以下JPG、PNG格式',
  time: '时间',
  selectTime: '选择时间',
  location: '地点',
  addLocationOptional: '添加地点（选填）',
  exportStarted: '导出任务已开始，请稍候...',
  templateDownloadStarted: '模板下载开始',
  uploadExcelOnly: '只能上传Excel文件!',
  fileSizeLimitExceeded: '文件大小不能超过10MB!',
  uploadSuccessProcessing: '文件上传成功，正在处理数据...',
  uploadFailed: '文件上传失败',

  selectUser: '选择用户',
  selectDepartment: '选择部门',
  viewSelectedOnly: '只看已选',
  supervisorLabel: "上司",
  supervisorNoSelection: "不设置上司",
  exportPermissionLabel: "允许导出",

  fileRecord: '文件记录',
  fileConfig: '文件配置',
  configName: '配置名',
  storage: '存储器',
  serialNumber: '编号',
  mainConfig: '主配置',
  test: '测试',
  addFileConfig: '添加文件配置',
  modifyConfig: "修改配置文件",
  enterConfigName: '请输入配置名',
  selectStorage: '请选择存储器',
  enterRemark: '请输入备注',
  filePath: '文件路径',
  fileName: '文件名',
  fileURL: '文件URL',
  fileSize: '文件大小',
  fileType: '文件类型',
  fileContent: '文件内容',
  uploadTime: '上传时间',
  enterFilePath: '请输入文件路径',
  uploadFile: '上传文件',
  // 角色管理
  roleName: '角色名称',
  roleKey: '角色标识',
  enterRoleName: '请输入角色名称',
  enterRoleKey: '请输入角色标识',
  selectRoleStatus: '请选择角色状态',
  roleId: '角色编号',
  roleType: '角色类型',
  menuPermissions: '菜单权限',
  roleOrder: '角色顺序',
  addRole: '新增角色',
  editRole: '修改角色',

  // 菜单管理
  routePath: '路由地址',
  isVisible: '是否显示',
  enterMenuName: '请输入菜单名称',
  enterRoutePath: '请输入路由地址',
  menuStatus: '菜单状态',
  alwaysShow: '总是显示',
  permissionString: '权限字符',
  componentPath: '组件路径',
  componentName: '组件名称',
  isCached: '是否缓存',
  addMenu: '添加菜单',
  editMenu: '修改菜单',

  // 部门管理
  enterDeptName: '请输入部门名称',
  selectDeptStatus: '请选择部门状态',
  selectLeader: '请选择负责人',
  selectParentDept: '请选择上级部门',
  enterPersonInCharge: "请输入负责人",
  enterEmail: '请输入邮箱',
  enterPhone: '请输入联系电话',
  addDept: '添加部门',
  editDept: '修改部门',
  import: '导入',

  // 用户管理
  enterNickname: '请输入用户昵称',
  selectAffiliatedDept: '请选择归属部门',
  userPassword: '用户密码',
  userGender: '用户性别',
  position: '职位',
  pleaseSelect: '请选择',
  addUser: '添加用户',
  editUser: '修改用户',
  assignRoles: '分配角色',
  role: '角色',
  enterUserId: '请输入用户编号',

  // 日志
  selectProcessStatus: '请选择处理状态',
  apiAccessLogDetail: 'API 访问日志详细',
  apiErrorLogDetail: 'API 异常日志详细',

  // 字典
  dictId: '字典编号',
  enterRemarkContent: '请输入备注内容',
  editDictType: '修改字典类型',
  addDictType: '添加字典类型',
  editDictData: '修改字典数据',
  addDictData: '添加字典数据',
  selectDataStatus: '请选择数据状态',
  enterDataKey: '请输入数据键值',
  startFilter: '开始筛选',
  // 用户认证
  enterUserPassword: '请输入用户密码',
  enterPassword: '请输入密码',

  // 路径配置
  basePath: '基础路径',
  enterBasePath: '请输入基础路径',

  // 主机配置
  hostAddress: '主机地址',
  enterHostAddress: '请输入主机地址',
  hostPort: '主机端口',
  enterHostPort: '请输入主机端口',

  // 连接设置
  connectionMode: '连接模式',
  activeMode: '主动模式',
  passiveMode: '被动模式',

  // 节点配置
  nodeAddress: '节点地址',
  enterNodeAddress: '请输入节点地址',

  // 存储配置
  storageBucket: '存储 Bucket',
  enterBucket: '请输入 Bucket',
  enterAccessKey: '请输入 AccessKey',
  enterAccessSecret: '请输入 AccessSecret',

  // 域名配置
  customDomain: '自定义域名',
  enterCustomDomain: '请输入自定义域名',

  uploadFile: "上传文件",
  dragAndDrop: "将文件拖到此处，或",
  clickToUpload: "点击上传",
  uploadTip: "提示：仅允许导入 JPG、PNG、GIF 格式文件！",
  uploadSuccess: "上传成功",
  uploadFailed: "上传失败",
  fileTypeError: "不支持的文件类型",
  fileSizeExceeded: "文件大小超过限制",

  // 个人字段
  title: '头衔',
  firstName: '姓氏',
  lastName: '名字',
  honour: '尊称',
  phoneMobile: '手机号码',
  titleEn: '头衔(英文)',
  firstNameEn: '姓氏(英文)',
  lastNameEn: '名字(英文)',
  honourEn: '尊称(英文)',
   phoneNumberLabel: '内地号码',

  // 组织字段
  companyName: '公司名称',
  department: '部门',
  jobTitle: '职位',
  email: '电子邮箱',
  phoneDirectLine: '直线电话',
  industryType: '行业类型',
  addressLine1: '地址行1',
  addressLine2: '地址行2',
  district: '区/县',
  area: '地区',
  city: '城市',
  companyNameEn: '公司名称(英文)',
  departmentEn: '部门(英文)',
  jobTitleEn: '职位(英文)',
  phoneOffice: '办公室电话',
  faxNumber: '传真号码',
  businessType: '业务类型',
  addressLine1En: '地址行1(英文)',
  addressLine2En: '地址行2(英文)',
  districtEn: '区/县(英文)',
  areaEn: '地区(英文)',
  countryEn: '国家(英文)',

  // 社交媒体
  linkedin: '领英',
  facebook: '脸书',
  instagram: 'Instagram',
  wechat: '微信',

  selectUserAndFields: '选择用户和字段',
  userList: '用户列表',
  searchUser: '搜索用户',
  fieldList: '字段列表',
  selectAll: '全选',
  clearAll: '清空',
  selectUserFirst: '请先在左侧选择用户',
  permissionPreview: '权限预览',
  noUserSelected: '未选择任何用户',
  permissionCount: '{count}项权限',
  noPermissionAssigned: '未分配任何字段权限',
  permissionSaved: '权限设置已保存',
  selectedUsersCount: '已选择 {count} 位用户',
  totalPermissionsCount: '共 {count} 项权限',
  requireAllFieldsForEdit: "需全选所有字段才能开启编辑权限",
  phoneFormatError: "请输入有效的香港电话号码(8或9位数字，可包含+852)",


  emailFormatError: "请输入有效的电子邮件地址",
  card: {
    uploadCardImage: '上传名片图片',
    uploadNewCard: '上传新名片',
    frontSide: '名片正面',
    frontSideRequired: '正面(必传)',
    backSide: '名片反面',
    backSideOptional: '反面(可选)',
    completeUploadTip: '请完善未完成的名片上传（灰色背景的名片缺少正面图片）',
    selectedCount: '已选择 {count} 张名片',
    invalidFileType: '请检查上传图片格式，仅支持PNG、JPG、JPEG图片格式',
    fileSizeExceeded: '请上传少于{size}MB的图片',
    uploadBackSideConfirm: '是否要上传名片反面？',
    uploadBackSide: '上传反面',
    deleteConfirm: '确定要删除这张名片吗？',
    selectAtLeastOne: '请至少选择一张完整的名片',
    completeUploadFirst: '请先完善未完成的名片上传',
    uploadFailed: '上传失败',
    deleteFailed: '删除名片失败',
    recognitionFailed: '名片识别失败',
    recognitionResult: '名片识别结果',
    successfulCards: '成功识别的名片 ({count}张)',
    failedCards: '识别失败的名片 ({count}张)',
    keepSelected: '保持选中',
    retryFront: '重新上传正面',
    retryBack: '重新上传反面',
    retry: '重新上传',
    recognizingCards: "正在识别名片，请稍候...",
     duplicateCards: "重复名片 ({count}张)",
    existingCardInfo: "系统中已存在的名片信息",
    viewDetail: "查看详情",
    name: "姓名",
    company: "公司",
    title: "职位",
    phone: "电话",
    creator: "创建者",
    createdAt: "创建时间"
  },
  common: {
    selectAll: '全选',
    tip: '提示',
    notNow: '暂不上传',
    deleteSuccess: '删除成功',
    confirmAndContinue: '确认并继续',
     unknown: "未知"
  },
  cardPreview: {
    front: '正面',
    back: '反面',
    flip: '正反调转',
    zoomIn: '放大',
    zoomOut: '缩小',
    rotateLeft: '向左旋转',
    rotateRight: '向右旋转',
    reset: '重置',
    close: '关闭',
    flipHint: '可翻转',
  },
  upload: {
    failed: '上传失败',
    deleteFailed: '文件删除失败',
    imageTypeError: '只能上传 JPG/PNG/GIF 格式的图片!',
    imageSizeError: '图片大小不能超过 {size}MB!',
    limitExceeded: '最多上传 {limit} 个文件，已选择 {selected} 个，本次尝试上传 {attempted} 个'
    },
    meeting: {
      updateSuccess: '商谈记录更新成功',
      updateFailed: '商谈记录更新失败',
      addSuccess: '商谈记录添加成功',
      addFailed: '商谈记录添加失败'
    },
    businessCard: {
      frontSide: '名片正面',
      backSide: '名片反面',
      deleteFront: '删除正面',
      deleteBack: '删除反面',
      preview: '放大预览',
      uploadFront: '上传正面图片',
      uploadBack: '上传反面图片',
      uploadHint: '支持JPG/PNG格式，不超过5MB',
      previewTitle: '图片预览',
      deleteConfirm: '确定删除该图片吗？',
      confirm: '确定',
      cancel: '取消',
      deleted: '已删除',
      uploadSuccess: '上传成功',
      uploadFailed: '上传失败',
      imageTypeError: '只能上传图片文件',
      sizeLimitError: '图片大小不能超过5MB',
      tip: '提示',
      uploading: "上传中..."
    },
    errors: {
      phone: {
        hkAreaCode: '请输入正确的香港区号（如+852）',
        hkNumber: '请输入8-9位数字电话号码',
        mainlandAreaCode: '请输入正确的内地区号（如+86）',
        mainlandNumber: '请输入11位有效内地手机号码（1开头）',
        required: '请输入电话号码',
         invalidNumber: "无效的电话号码",
      invalidFormat: "电话号码格式不正确",
      invalidAreaCode: "无效的国家/地区代码",
      mismatch: "国家代码与号码不匹配"
      },
      fields: {
        phoneMobile: '流动电话',
        phoneMainland: '内地号码',
        phoneOffice: '办公电话',
        phoneDirectLine: '直线电话'
      }
    },
    companyManagement: {
      confirmDeleteCompany: '确定要删除该公司吗？删除后将无法恢复！',
      warning: '警告',
      deleteSuccess: '删除成功',
      deleteFailed: '删除失败',
      deleteCancelled: '已取消删除',
      deleteButton: '删除',
      cancelButton: '取消'
    },
    exportApproval:'Excel导出审批',
      approvalId: '审批编号',
      enterApprovalId: '请输入审批编号',
      applicant: '申请人',
      enterApplicant: '请输入申请人',
      applyTime: '申请时间',
      approvalStatus: '审批状态',
      selectApprovalStatus: '请选择审批状态',
      exportStatus: '导出状态',
      selectExportStatus: '请选择导出状态',
      pendingApproval: '待审批',
      pendingExport: '待导出',
      exportCount: '导出数量',
      items: '条',
      exportRemark: '导出备注',
      approvalRemark: '审批备注',
      approver: '审批人',
      approvalTime: '审批时间',
      approvalDialogTitle: '导出审批',
      enterApprovalRemark: '请输入审批备注',
      approvalRemarkRequired: '审批备注不能为空',
      confirmApprove: '确认要通过此导出申请吗？',
      confirmReject: '确认要拒绝此导出申请吗？',
      approveSuccess: '审批通过成功',
      rejectSuccess: '审批拒绝成功',
      confirmExport: '确认要导出这{0}条数据吗？',
      exportSuccess: '导出成功',
      approve: '同意',
      reject: '拒绝',
      noData: "暂无数据",
      excelExportApplication: 'Excel Export Application',
      remarks: '留言备注',
      pleaseEnterRemarks: '请输入备注信息(选填)',
      todoNotification: {
      title: '待办事项',
      takeAction: '前往处理',
      ignore: '暂时忽略'
    }
   

};