<template>
  <div>
    <base-dialog
      v-model="dialogVisible"
      :title="$t('excelExportApplication')"
      width="500px"
      @confirm="handleSubmit"
      @cancel="handleCancel"
    >
      <template v-slot:content>
        <div class="excel-export-form">
          <!-- 申请人 -->
          <div class="form-item">
            <div class="form-label">{{ $t("applicant") }}</div>
            <div class="form-value">{{ form.applicant }}</div>
          </div>

          <!-- 导出条数 -->
          <div class="form-item">
            <div class="form-label">{{ $t("exportCount") }}</div>
            <div class="form-value">{{ form.exportCount }}</div>
          </div>

          <!-- 审批人 -->
          <div class="form-item">
            <div class="form-label">{{ $t("approver") }}</div>
            <div class="form-value">{{ form.approver }}</div>
          </div>

          <!-- 留言备注 -->
          <div class="form-item">
            <div class="form-label">{{ $t("remarks") }}</div>
            <el-input
              v-model="form.requesterComment"
              type="textarea"
              :rows="3"
              :placeholder="$t('pleaseEnterRemarks')"
              maxlength="200"
              show-word-limit
            />
          </div>
        </div>
      </template>
    </base-dialog>
  </div>
</template>

<script>
import BaseDialog from "@/components/PcmDialog";
import { createRequest } from "@/api/pcm/approval";
export default {
  components: { BaseDialog },
  props: {
    // 审批人
    approver: {
      type: String,
      default: "",
    },
    // 导出条数
    exportCount: {
      type: Number,
      default: 0,
    },
    //勾选名片
    selectedIds: {
      type: Array,
      default: () => [],
    },
    // 导出查询参数
    queryParams: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialogVisible: false,
      form: {
        applicant: "", // 当前登录用户
        exportCount: 0,
        approver: "",
        requesterComment: "",
        pageReqVO: {},
      },
    };
  },
  created() {
    // 获取当前登录用户作为申请人
    this.form.applicant = this.$store.getters.nickname || "";
  },
  methods: {
    open() {
      this.dialogVisible = true;
      // 初始化表单数据
      this.form = {
        applicant: this.$store.getters.nickname || "",
        exportCount: this.exportCount,
        approver: this.approver,
        requesterComment: "",
        pageReqVO: this.queryParams,
      };
    },

    handleSubmit() {
      const requestData = {
        ...this.form, 
        pageReqVO: {
          ...this.form.pageReqVO, 
          ids: this.selectedIds,
        },
      };
      console.log('提交数据:', JSON.parse(JSON.stringify(requestData))); // 深度拷贝打印
      createRequest(requestData)
        .then((res) => {
          this.$modal.msgSuccess("成功創建導出申請！");
          this.dialogVisible = false;
        })
        .catch((error) => {
          this.$modal.msgError("創建導出申請失敗");
          console.error("Export request failed:", error);
        });
    },

    handleCancel() {
      this.dialogVisible = false;
    },
  },
};
</script>

<style scoped>
.excel-export-form {
  padding: 10px 20px;
}

.form-item {
  margin-bottom: 20px;
}

.form-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-value {
  font-size: 14px;
  color: #303133;
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
}
</style>