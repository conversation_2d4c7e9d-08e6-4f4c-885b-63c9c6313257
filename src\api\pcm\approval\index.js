import request from '@/utils/request'

// 提交名片導出申請
export function createRequest(data) {
  return request({
    url: '/pcm/export/request',
    method: 'post',
    data: data
  })
}

// 獲取審批列表
export function approvalList(data) {
  return request({
    url: '/pcm/export/approval-list',
    method: 'post',
    data: data
  })
}

// 獲取導出列表
export function exportList(data) {
  return request({
    url: '/pcm/export/export-list',
    method: 'post',
    data: data
  })
}

// 審批匯出請求
export function approve(data) {
  return request({
    url: '/pcm/export/approve',
    method: 'post',
    data: data
  })
}

// 導出已審批的名片 Excel
export function downloadExport(query) {
  return request({
    url: '/pcm/export/approved',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

//獲取待辦通知
export function notification() {
  return request({
    url: '/pcm/export/todo-notification',
    method: 'get'
  })
}

//檢查是否達到導出限制
export function checkRestriction(data) {
  return request({
    url: '/pcm/export/check-restriction',
    method: 'post',
    data: data
  })
}

//无鉴权通过guid获取申请记录
export function getApplyByGuid(params) {
  return request({
    url: '/pcm/export/getApproveByGuid',
    method: 'get',
    params
  })
}

//无鉴权审批申请
export function approveByEmail(params) {
  return request({
    url: '/pcm/export/approveByEmail',
    method: 'get',
    params
  })
}
