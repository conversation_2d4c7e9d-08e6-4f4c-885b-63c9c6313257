<template>
  <div class="tools-container">
    <!-- 左侧二级菜单 -->
    <div v-if="!$isMobile() || ($isMobile() && currentSetup === 1)" class="left-menu">
      <div class="menu-title">{{$t('toolbox')}}</div>
      <!-- 导出相关菜单 -->
      <div
        class="menu-item"
        :class="{ active: activeMenu === 'exportExcel' }"
        @click="changeMenu('exportExcel')"
      >
        <img :src="getIcon('exportExcel')" class="menu-icon" />
        {{$t('exportToExcel')}}
      </div>
      <div
        class="menu-item"
        :class="{ active: activeMenu === 'importExcel' }"
        @click="changeMenu('importExcel')"
      >
        <img :src="getIcon('importExcel')" class="menu-icon" />
        {{$t('batchImportCards')}}
      </div>
      <!-- 分割线 -->
      <div class="menu-divider"></div>
      <!-- 其他功能菜单 -->
      <div
        class="menu-item"
        :class="{ active: activeMenu === 'changePassword' }"
        @click="changeMenu('changePassword')"
      >
        <img :src="getIcon('changePassword')" class="menu-icon" />
        {{$t('changePassword')}}
      </div>
      <div
        class="menu-item"
        :class="{ active: activeMenu === 'companyTag' }"
        @click="changeMenu('companyTag')"
      >
        <img :src="getIcon('companyTag')" class="menu-icon" />
        {{$t('companyTagManagement')}}
      </div>
      <div
        class="menu-item"
        :class="{ active: activeMenu === 'recycleBin' }"
        @click="changeMenu('recycleBin')"
      >
        <img :src="getIcon('recycleBin')" class="menu-icon" />
        {{$t('cardRecycleBin')}}
      </div>
    </div>

    <!-- 右侧内容 -->
    <div v-if="!$isMobile() || ($isMobile() && currentSetup === 2)" class="right-content">
      <div class="content-title">
        <img
          src="@/assets/pcm/card-detail/back.png"
          alt="返回箭头"
          class="back-arrow hidden-sm-and-up"
          @click="goBack()"
        />
        <span>{{ activeMenuLabel }}</span>
      </div>
      <div class="content-divider"></div>
      <div class="content-body">
        <!-- 内容区域暂时留空 -->
        <p v-if="activeMenu === 'exportExcel'">
          <CardExport />
        </p>
        <p v-if="activeMenu === 'importExcel'">
          <CardImport />
        </p>
        <p v-if="activeMenu === 'changePassword'">
          <profile />
        </p>
        <p v-if="activeMenu === 'companyTag'">
          <tag />
        </p>
        <p v-if="activeMenu === 'recycleBin'">
          <recycle />
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import profile from "@/views/pcm/tool/profile";
import tag from "@/views/pcm/tool/tag";
import recycle from "@/views/pcm/tool/recycle";
import CardExport from "@/views/pcm/tool/export";
import CardImport from "@/views/pcm/tool/import";
export default {
  components: {
    profile,
    tag,
    recycle,
    CardExport,
    CardImport,
  },
  watch: {
    $route(to, from) {
      // 监听路由变化，获取查询参数
      this.activeMenu = to.query.menuName;
    },
    activeMenu(newVal, oldVal) {
      this.$router.push({
        query: {
          ...this.$route.query,
          menuName: newVal,
        },
      });
    },
  },
  data() {
    return {
      activeMenu: "exportExcel", // 默认激活的菜单
      currentSetup: 1,
    };
  },
  computed: {
    activeMenuLabel() {
      switch (this.activeMenu) {
        case "exportExcel":
          return this.$t('exportToExcel');
        case "importExcel":
          return this.$t('batchImportCards');
        case "changePassword":
          return this.$t('changePassword');
        case "companyTag":
          return this.$t('companyTagManagement');
        case "recycleBin":
          return this.$t('cardRecycleBin');
        default:
          return "";
      }
    },
  },
  mounted() {
    this.activeMenu = this.$route.query.menuName;
  },
  methods: {
    goBack() {
      this.currentSetup = 1
    },
    handleMenuChangeByMobileFn() {
      this.currentSetup = 2
    },
    changeMenu(menu) {
      this.activeMenu = menu;
      if (this.$isMobile()) {
        this.handleMenuChangeByMobileFn()
      }
    },
    getIcon(menu) {
      // 根据菜单项返回对应的图标路径
      switch (menu) {
        case "exportExcel":
          return require("@/assets/pcm/tool/icon-export.png");
        case "exportCRM":
          return require("@/assets/pcm/tool/icon-crm.png");
        case "exportGoogle":
          return require("@/assets/pcm/tool/icon-google.png");
        case "exportOutlook":
          return require("@/assets/pcm/tool/icon-outlook.png");
        case "saveToPrivate":
          return require("@/assets/pcm/tool/icon-file.png");
        case "importExcel":
          return require("@/assets/pcm/tool/icon-export.png");
        case "changePassword":
          return require("@/assets/pcm/tool/icon-password.png");
        case "companyTag":
          return require("@/assets/pcm/tool/icon-tag.png");
        case "recycleBin":
          return require("@/assets/pcm/tool/icon-delete.png");
        default:
          return "";
      }
    },
  },
};
</script>

<style scoped>
.back-arrow {
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  position: absolute;
  width: 24px;
  height: 24px;
  margin-right: 8px;
  cursor: pointer;
}
.tools-container {
  display: flex;
  height: 100%;
  background-color: #f0f4fa;
}

.left-menu {
  flex: 2;
  background-color: white;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.menu-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 10px;
  font-size: 14px;
  color: #1f1f1f;
  font-weight: 400;
}

.menu-item:hover {
  background-color: #ed6c001a;
  color: #ed6c00;
}

.menu-item.active {
  background-color: #ed6c001a;
  color: #ed6c00;
}

.menu-icon {
  width: 16px;
  height: 16px;
  margin-right: 10px;
  filter: brightness(0) saturate(100%) invert(0%) sepia(0%) saturate(7500%)
    hue-rotate(0deg) brightness(100%) contrast(100%);
}

.menu-item:hover .menu-icon,
.menu-item.active .menu-icon {
  filter: brightness(0) saturate(100%) invert(50%) sepia(100%) saturate(1000%)
    hue-rotate(0deg) brightness(100%) contrast(100%);
}

.menu-divider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 20px 0;
}

.right-content {
  overflow-x: scroll;
  flex: 8;
  margin-left: 10px;
  background-color: white;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

@media screen and (max-width: 768px) {
  .right-content {
    margin-left: 0px;
  }
  .content-title {
    position: relative;
    text-align: center;
  }
}

.content-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}

.content-divider {
  height: 1px;
  background-color: #e0e0e0;
  margin-bottom: 20px;
}

.content-body {
  color: #666;
}
</style>