<template>
  <div class="attendance-container" :class="{ 'pt-theme': isPTUser, 'unit-theme': !isPTUser }">
    <!-- 页面标题 -->
    <div class="header-section">
      <div class="breadcrumb">
        <span @click="goBack" class="breadcrumb-link">上班簽到表</span>
        <span class="separator"> > </span>
        <span @click="goBack" class="breadcrumb-link">區梓俊</span>
        <span class="separator"> > </span>
        <span class="current-page">{{ currentDate }}</span>
      </div>
      <div class="status-indicator">
        <span class="status-text">({{ isCompleted ? '填寫完畢' : '填寫中' }})</span>
      </div>
    </div>

    <!-- 工作节数列表 -->
    <div class="sessions-container">
      <div v-for="(session, index) in workSessions" :key="session.id" class="session-card">
        <!-- 节数标题 -->
        <div class="session-header">
          <h3>第 {{ session.sessionNumber }} 節 (共 {{ totalSessions }} 節) > {{ session.timeRange }} > {{ session.unit }} > {{ session.position }}</h3>
        </div>

        <!-- 签到部分 -->
        <div class="attendance-section">
          <div class="attendance-row">
            <div class="field-group">
              <label class="field-label">簽到時間</label>
              <el-date-picker
                v-if="!session.hasCheckInRecord || session.isEditing"
                v-model="session.checkInTime"
                type="datetime"
                placeholder="選擇簽到時間"
                format="yyyy/MM/dd HH:mm"
                value-format="yyyy/MM/dd HH:mm"
                @change="onCheckInTimeChange(session)"
                class="time-input"
              />
              <span v-else class="time-display">{{ session.checkInTime }}</span>

              <label class="field-label">紀錄創建時間</label>
              <span class="time-display">{{ session.checkInRecordTime }}</span>
            </div>
          </div>

          <div class="attendance-row">
            <div class="checkbox-group">
              <el-checkbox
                v-model="session.checkInAbsent"
                @change="onAbsentChange(session, 'checkIn')"
                :disabled="session.hasCheckInRecord && !session.isEditing"
              >
                因事缺席 (請於備註輸入原因)
              </el-checkbox>
            </div>
          </div>

          <div class="attendance-row">
            <div class="field-group">
              <label class="field-label">創建者</label>
              <span class="creator-display">{{ session.checkInCreator }}</span>

              <label class="field-label">備註</label>
              <el-input
                v-if="!session.hasCheckInRecord || session.isEditing"
                v-model="session.checkInRemark"
                placeholder="已到公司，但遲了打卡"
                class="remark-input"
              />
              <span v-else class="remark-display">{{ session.checkInRemark }}</span>

              <el-button
                v-if="!session.hasCheckInRecord"
                type="primary"
                @click="checkIn(session)"
                :disabled="!canCheckIn(session)"
                class="action-btn"
              >
                打卡
              </el-button>
              <el-button
                v-else-if="!session.isEditing"
                type="default"
                @click="editCheckIn(session)"
                class="action-btn"
              >
                修改
              </el-button>
              <el-button
                v-else
                type="primary"
                @click="saveCheckIn(session)"
                class="action-btn"
              >
                儲存
              </el-button>
            </div>
          </div>
        </div>

        <!-- 签走部分 -->
        <div class="attendance-section">
          <div class="attendance-row">
            <div class="field-group">
              <label class="field-label">簽走時間</label>
              <el-date-picker
                v-if="!session.hasCheckOutRecord || session.isEditingOut"
                v-model="session.checkOutTime"
                type="datetime"
                placeholder="選擇簽走時間"
                format="yyyy/MM/dd HH:mm"
                value-format="yyyy/MM/dd HH:mm"
                @change="onCheckOutTimeChange(session)"
                class="time-input"
              />
              <span v-else class="time-display">{{ session.checkOutTime }}</span>

              <label class="field-label">紀錄創建時間</label>
              <span class="time-display">{{ session.checkOutRecordTime }}</span>
            </div>
          </div>

          <div class="attendance-row">
            <div class="checkbox-group">
              <el-checkbox
                v-model="session.checkOutAbsent"
                @change="onAbsentChange(session, 'checkOut')"
                :disabled="session.hasCheckOutRecord && !session.isEditingOut"
              >
                因事缺席 (請於備註輸入原因)
              </el-checkbox>
            </div>
          </div>

          <div class="attendance-row">
            <div class="field-group">
              <label class="field-label">創建者</label>
              <span class="creator-display">{{ session.checkOutCreator }}</span>

              <label class="field-label">備註</label>
              <el-input
                v-if="!session.hasCheckOutRecord || session.isEditingOut"
                v-model="session.checkOutRemark"
                placeholder=""
                class="remark-input"
              />
              <span v-else class="remark-display">{{ session.checkOutRemark }}</span>

              <el-button
                v-if="!session.hasCheckOutRecord"
                type="primary"
                @click="checkOut(session)"
                :disabled="!canCheckOut(session)"
                class="action-btn"
              >
                打卡
              </el-button>
              <el-button
                v-else-if="!session.isEditingOut"
                type="default"
                @click="editCheckOut(session)"
                class="action-btn"
              >
                修改
              </el-button>
              <el-button
                v-else
                type="primary"
                @click="saveCheckOut(session)"
                class="action-btn"
              >
                儲存
              </el-button>
            </div>
          </div>
        </div>

        <!-- 分割线 (除了最后一个节数) -->
        <div v-if="index < workSessions.length - 1" class="section-divider"></div>
      </div>
    </div>

    <!-- 分割线 -->
    <div class="section-divider"></div>

    <!-- 确认工作时数和核实记录部分 -->
    <div class="work-hours-verification-section">
      <h3>確認工作時數</h3>
      <div class="work-hours-description">
        必須有完整打卡紀錄，方可確認實際工作時數，如打卡紀錄一經修改，必須重新核實實際工作時數。
      </div>

      <div class="work-hours-row">
        <div class="field-group">
          <label class="field-label">本日已編更時數</label>
          <el-input
            v-model="scheduledWorkHours"
            class="hours-input"
            readonly
          />

          <label class="field-label">參考工作時數</label>
          <div class="reference-hours">
            <span v-if="!allSessionsCompleted">未有完整打卡紀錄，未能計算</span>
            <span v-else>{{ calculatedWorkHours }}</span>
            <div class="reference-note">此為以實到離退時間計算之參考時數</div>
          </div>
        </div>
      </div>

      <div class="work-hours-row">
        <div class="field-group">
          <label class="field-label">實際工作時數</label>
          <el-input
            v-model="actualWorkHours"
            class="hours-input"
            :readonly="!canConfirmWorkHours"
          />

          <label class="field-label">備註</label>
          <div class="remark-section">
            <el-input
              v-model="workHoursRemark"
              placeholder="如實際工作時數少於已編更時數，請於備註輸入原因。"
              class="remark-textarea"
              type="textarea"
              :rows="2"
            />
          </div>
        </div>
      </div>

      <div class="work-hours-row">
        <div class="field-group">
          <label class="field-label">核實者</label>
          <el-input
            v-model="verifier"
            class="hours-input"
            readonly
          />

          <label class="field-label">核實時間</label>
          <el-input
            v-model="verifyTime"
            class="hours-input"
            readonly
          />

          <el-button
            v-if="canPTConfirm"
            type="warning"
            @click="ptConfirm"
            class="confirm-btn"
          >
            兼職/臨時工確認
          </el-button>
        </div>
      </div>
    </div>

    <div class="section-divider"></div>

    <!-- 核实记录 -->
    <div class="verification-part">
      <h3>核實紀錄</h3>
      <div class="verification-row">
        <div class="field-group">
          <label class="field-label">同工核實</label>
          <el-input
            :value="verificationRecord?.verifiedBy || ''"
            class="verification-input"
            readonly
          />

          <label class="field-label">時間</label>
          <el-input
            :value="verificationRecord?.verifiedTime || ''"
            class="verification-input"
            readonly
          />
        </div>
      </div>

      <div class="verification-row">
        <div class="field-group">
          <label class="field-label">負責人核實</label>
          <el-input
            :value="verificationRecord?.supervisorVerified || ''"
            class="verification-input"
            readonly
          />

          <label class="field-label">時間</label>
          <el-input
            :value="verificationRecord?.supervisorVerifiedTime || ''"
            class="verification-input"
            readonly
          />
        </div>
      </div>

      <div class="verification-row full-width">
        <div class="field-group">
          <label class="field-label">負責人核實</label>
          <el-input
            :value="verificationRecord?.supervisorRemark || ''"
            class="verification-remark"
            readonly
          />
        </div>
      </div>
    </div>
    </div>
  </div>
</template>

<script>
import * as AttendanceApi from '@/api/workflow/attendance'

export default {
  name: 'WorkflowAttendance',
  data() {
    return {
      // 用户类型
      isPTUser: true, // 从用户信息获取

      // 当前日期
      currentDate: '2025/09/25',

      // 总节数
      totalSessions: 2,

      // 是否已完成
      isCompleted: false,

      // 工作节数数据
      workSessions: [
        {
          id: 1,
          sessionNumber: 1,
          timeRange: '09:00 - 13:00',
          unit: 'ABC學校',
          position: '入校導師',
          
          // 签到相关
          checkInTime: '2025/09/25 09:00',
          checkInRecordTime: '2025/09/25 09:14',
          checkInCreator: '區梓俊',
          checkInRemark: '已到公司，但遲了打卡',
          checkInAbsent: false,
          checkInLocked: false,
          hasCheckInRecord: true,
          isEditing: false,
          
          // 签走相关
          checkOutTime: '2025/09/25 13:14',
          checkOutRecordTime: '2025/09/25 13:14',
          checkOutCreator: '區梓俊',
          checkOutRemark: '',
          checkOutAbsent: false,
          checkOutLocked: false,
          hasCheckOutRecord: true,
          isEditingOut: false
        },
        {
          id: 2,
          sessionNumber: 2,
          timeRange: '18:00 - 21:00',
          unit: 'ABC學校',
          position: '入校導師',
          
          // 签到相关
          checkInTime: '2025/09/25 17:33',
          checkInRecordTime: '2025/09/25 17:33',
          checkInCreator: '區梓俊',
          checkInRemark: '',
          checkInAbsent: false,
          checkInLocked: false,
          hasCheckInRecord: true,
          isEditing: false,
          
          // 签走相关
          checkOutTime: '2025/9/25 21:00',
          checkOutRecordTime: '',
          checkOutCreator: '區梓俊',
          checkOutRemark: '',
          checkOutAbsent: false,
          checkOutLocked: false,
          hasCheckOutRecord: false,
          isEditingOut: false
        }
      ],

      // 工作时数
      actualWorkHours: 0,
      scheduledWorkHours: 7, // 编更时数
      workHoursRemark: '', // 工作时数备注
      calculatedWorkHours: 7.68, // 计算的工作时数
      verifier: '區梓俊', // 核实者
      verifyTime: '2025/09/25 22:34', // 核实时间

      // 核实记录
      verificationRecord: {
        verifiedBy: '區',
        verifiedTime: '2025/09/25 22:34',
        supervisorVerified: '',
        supervisorVerifiedTime: '',
        supervisorRemark: ''
      }
    }
  },

  computed: {
    canConfirmWorkHours() {
      return this.actualWorkHours > 0 && this.allSessionsCompleted
    },

    canPTConfirm() {
      return this.isPTUser && this.actualWorkHours > 0 && this.allSessionsCompleted
    },

    allSessionsCompleted() {
      return this.workSessions.every(session => 
        session.hasCheckInRecord && session.hasCheckOutRecord
      )
    }
  },

  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },

    // 检查是否可以签到
    canCheckIn(session) {
      const now = new Date()
      const sessionStart = new Date(session.timeRange.split(' - ')[0])
      const allowedTime = new Date(sessionStart.getTime() - 30 * 60 * 1000) // 提前30分钟
      
      return now >= allowedTime
    },

    // 检查是否可以签走
    canCheckOut(session) {
      return session.hasCheckInRecord
    },

    // 签到时间变化
    onCheckInTimeChange(session) {
      if (!this.canCheckIn(session)) {
        this.$message.warning('根據編更紀錄，你的工作時間尚未開始。')
        session.checkInTime = ''
      }
    },

    // 签走时间变化
    onCheckOutTimeChange(session) {
      // 可以添加签走时间验证逻辑
      console.log('签走时间变化:', session)
    },

    // 编辑签到
    editCheckIn(session) {
      session.isEditing = true
    },

    // 保存签到
    saveCheckIn(session) {
      session.isEditing = false
      session.checkInRecordTime = new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }).replace(/\//g, '/').replace(',', '')
    },

    // 编辑签走
    editCheckOut(session) {
      session.isEditingOut = true
    },

    // 保存签走
    saveCheckOut(session) {
      session.isEditingOut = false
      session.checkOutRecordTime = new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }).replace(/\//g, '/').replace(',', '')
    },

    // PT确认
    ptConfirm() {
      this.verifier = '區梓俊'
      this.verifyTime = new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }).replace(/\//g, '/').replace(',', '')

      this.verificationRecord.verifiedBy = '區'
      this.verificationRecord.verifiedTime = this.verifyTime

      this.$message.success('兼職/臨時工確認成功')
    },

    // 缺席状态变化
    onAbsentChange(session, type) {
      if (type === 'checkIn' && session.checkInAbsent) {
        session.checkInTime = null
      } else if (type === 'checkOut' && session.checkOutAbsent) {
        session.checkOutTime = null
      }
    },

    // 签到
    async checkIn(session) {
      if (session.checkInAbsent && !session.checkInRemark) {
        this.$message.error('請於備註輸入缺席原因')
        return
      }

      try {
        const data = {
          sessionId: session.id,
          checkInTime: session.checkInAbsent ? null : session.checkInTime,
          remark: session.checkInRemark,
          isAbsent: session.checkInAbsent
        }

        await AttendanceApi.createOrUpdateCheckIn(data)
        
        session.hasCheckInRecord = true
        session.checkInRecordTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        
        if (!session.checkInTime && !session.checkInAbsent) {
          session.checkInTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        }

        this.$message.success('簽到成功')
      } catch (error) {
        this.$message.error('簽到失敗: ' + error.message)
      }
    },

    // 签走
    async checkOut(session) {
      if (session.checkOutAbsent && !session.checkOutRemark) {
        this.$message.error('請於備註輸入缺席原因')
        return
      }

      try {
        const data = {
          sessionId: session.id,
          checkOutTime: session.checkOutAbsent ? null : session.checkOutTime,
          remark: session.checkOutRemark,
          isAbsent: session.checkOutAbsent
        }

        await AttendanceApi.createOrUpdateCheckOut(data)
        
        session.hasCheckOutRecord = true
        session.checkOutRecordTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        
        if (!session.checkOutTime && !session.checkOutAbsent) {
          session.checkOutTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        }

        this.$message.success('簽走成功')

        // 如果是当日最后一节，提示确认工作时数
        if (session.sessionNumber === this.totalSessions) {
          this.$message.info('請於簽走後立即確認工作時數紀錄。')
        }
      } catch (error) {
        this.$message.error('簽走失敗: ' + error.message)
      }
    },

    // 修改签到
    updateCheckIn(session) {
      session.checkInLocked = false
      this.$message.info('可以修改簽到時間和備註')
    },

    // 修改签走
    updateCheckOut(session) {
      session.checkOutLocked = false
      this.$message.info('可以修改簽走時間和備註')
    },

    // 保存签到
    async saveCheckIn(session) {
      try {
        const data = {
          sessionId: session.id,
          checkInTime: session.checkInTime,
          remark: session.checkInRemark,
          isAbsent: session.checkInAbsent
        }

        await AttendanceApi.updateAttendanceRecord(data)
        session.checkInRecordTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        
        this.$message.success('簽到記錄已更新')
      } catch (error) {
        this.$message.error('保存失敗: ' + error.message)
      }
    },

    // 确认工作时数
    async confirmWorkHours() {
      if (this.actualWorkHours > this.scheduledWorkHours) {
        this.$message.error('如實際工作時數已超出編更時數，將無法儲存，請聯繫直屬上司。')
        return
      }

      if (this.actualWorkHours < this.scheduledWorkHours && !this.hasRemarkForLessHours()) {
        this.$message.error('如實際工作時數少於編更時數，請於備註輸入原因。')
        return
      }

      try {
        const data = {
          date: this.currentDate,
          actualWorkHours: this.actualWorkHours,
          scheduledWorkHours: this.scheduledWorkHours,
          sessions: this.workSessions
        }

        await AttendanceApi.confirmWorkHours(data)
        this.$message.success('工作時數確認成功')
      } catch (error) {
        this.$message.error('確認失敗: ' + error.message)
      }
    },

    // PT同工确认
    async ptConfirmWorkHours() {
      try {
        const data = {
          date: this.currentDate,
          actualWorkHours: this.actualWorkHours,
          verifiedBy: '當前用戶', // 从用户信息获取
          verifiedTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
        }

        await AttendanceApi.ptConfirmWorkHours(data)
        
        this.verificationRecord = {
          verifiedBy: data.verifiedBy,
          verifiedTime: data.verifiedTime
        }

        this.$message.success('兼職/臨時工確認成功')
      } catch (error) {
        this.$message.error('確認失敗: ' + error.message)
      }
    },

    // 检查是否有备注说明工时少的原因
    hasRemarkForLessHours() {
      return this.workSessions.some(session =>
        session.checkInRemark || session.checkOutRemark
      )
    },

    // 加载考勤数据
    async loadAttendanceData() {
      try {
        // 这里可以调用API获取实际数据
        // const data = await AttendanceApi.getWorkTimeRecord(this.currentDate)
        // 处理返回的数据...
      } catch (error) {
        console.error('加载数据失败:', error)
      }
    }
  },

  mounted() {
    // 页面加载时获取数据
    this.loadAttendanceData()
  }
}
</script>

<style>
/* 主题样式 */
.attendance-container {
  min-height: 100vh;
  padding: 20px;
  background-color: #f5f5f5;
}

.pt-theme {
  background-color: #fff3e0; /* 淺橙色背景 */
}

.unit-theme {
  background-color: #ffffff; /* 白色背景 */
}

/* 头部区域 */
.header-section {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  font-size: 16px;
}

.breadcrumb-link {
  color: #409eff;
  cursor: pointer;
  text-decoration: none;
}

.breadcrumb-link:hover {
  text-decoration: underline;
}

.separator {
  margin: 0 8px;
  color: #666;
}

.current-page {
  color: #333;
  font-weight: bold;
}

.status-indicator {
  margin-left: auto;
}

.status-text {
  color: #e6a23c;
  font-weight: bold;
}

/* 工作节数容器 */
.sessions-container {
  background: #fff3e0;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.session-card {
  margin-bottom: 20px;
}

.session-card:last-child {
  margin-bottom: 0;
}

.session-header h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

/* 考勤部分 */
.attendance-section {
  margin-bottom: 15px;
}

.attendance-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  min-height: 40px;
}

.field-group {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 15px;
}

.field-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  min-width: 80px;
  text-align: left;
}

.time-input {
  width: 180px;
}

.time-display {
  font-size: 14px;
  color: #333;
  background: #f5f5f5;
  padding: 8px 12px;
  border-radius: 4px;
  min-width: 150px;
}

.creator-display {
  font-size: 14px;
  color: #333;
  background: #f5f5f5;
  padding: 8px 12px;
  border-radius: 4px;
  min-width: 80px;
}

.remark-input {
  width: 200px;
}

.remark-display {
  font-size: 14px;
  color: #333;
  background: #f5f5f5;
  padding: 8px 12px;
  border-radius: 4px;
  min-width: 150px;
}

.action-btn {
  margin-left: auto;
}

.checkbox-group {
  padding-left: 95px;
}

/* 分割线 */
.section-divider {
  height: 1px;
  background: #eee;
  margin: 20px 0;
}

/* 工作时数确认和核实记录部分 */
.work-hours-verification-section {
  background: #fff3e0;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.work-hours-verification-section h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.work-hours-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
  line-height: 1.5;
}

.work-hours-row {
  margin-bottom: 15px;
}

.hours-input {
  width: 120px;
}

.reference-hours {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.reference-note {
  font-size: 12px;
  color: #999;
}

.remark-section {
  flex: 1;
}

.remark-textarea {
  width: 100%;
  max-width: 400px;
}

.confirm-btn {
  margin-left: auto;
}

/* 核实记录样式 */
.verification-part h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.verification-row {
  margin-bottom: 15px;
}

.verification-row.full-width .field-group {
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
}

.verification-input {
  width: 120px;
}

.verification-remark {
  width: 100%;
  max-width: 400px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .attendance-container {
    padding: 10px;
  }

  .attendance-row {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .session-info {
    flex-direction: column;
    gap: 5px;
  }

  .work-hours-content {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .record-info {
    flex-direction: column;
    gap: 10px;
  }

  .action-buttons {
    margin-left: 0;
    justify-content: flex-start;
  }
}

/* Element UI 组件自定义样式 */
.el-date-editor {
  width: 100%;
}

.el-input-number {
  width: 120px;
}

.el-checkbox {
  font-size: 14px;
}

.el-button {
  min-width: 80px;
}

/* 输入框样式 */
.el-input__inner {
  background-color: #f5f5f5 !important;
  border: 1px solid #ddd !important;
}

.el-input__inner:focus {
  background-color: #fff !important;
  border-color: #409eff !important;
}

.el-input__inner[readonly] {
  background-color: #f5f5f5 !important;
  color: #666 !important;
}

.el-textarea__inner {
  background-color: #f5f5f5 !important;
  border: 1px solid #ddd !important;
}

.el-textarea__inner:focus {
  background-color: #fff !important;
  border-color: #409eff !important;
}

.el-input-number .el-input__inner {
  background-color: #f5f5f5 !important;
  border: 1px solid #ddd !important;
}
</style>
