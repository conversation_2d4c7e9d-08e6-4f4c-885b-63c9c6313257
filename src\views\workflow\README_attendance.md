# 签到/签走页面使用说明

## 功能概述

这个页面实现了完整的工作时间签到/签走功能，包括：

1. **多节数工作时间管理** - 支持一天内多个工作时段的签到签走
2. **智能时间验证** - 防止提前签到，确保工作时间的准确性
3. **缺席记录功能** - 支持因事缺席的记录和备注
4. **工作时数确认** - 实际工作时数的计算和确认
5. **PT同工确认** - 兼职/临时工的特殊确认流程
6. **记录修改功能** - 支持对已有记录的修改和更新

## 页面结构

### 1. 页面头部
- 面包屑导航：显示当前位置和日期
- 状态指示器：显示当前填写状态

### 2. 工作节数卡片
每个工作节数包含：
- **节数信息**：第X节，时间范围，工作单位，职位
- **签到部分**：签到时间、记录创建时间、创建者、备注
- **签走部分**：签走时间、记录创建时间、创建者、备注
- **操作按钮**：打卡、修改、储存

### 3. 工作时数确认
- 实际工作时数输入（以0.25小时为单位）
- 编更时数显示
- 确认和PT确认按钮
- 核实记录显示

## 业务规则

### 签到规则
1. **时间限制**：只能在节数开始前30分钟内签到
2. **缺席处理**：勾选"因事缺席"时，签到时间为空，必须填写备注
3. **自动记录**：如果没有手动输入时间，打卡时自动记录当前时间

### 签走规则
1. **前置条件**：必须先完成签到才能签走
2. **最后节数提醒**：当日最后一节签走后，提示确认工作时数

### 工作时数确认规则
1. **超时限制**：实际工作时数不能超过编更时数
2. **少时备注**：实际工作时数少于编更时数时，必须在备注中说明原因
3. **完整记录**：所有节数都必须完成签到签走才能确认工作时数

## 技术实现

### API接口
- `getWorkTimeRecord()` - 获取工作时间记录
- `createOrUpdateCheckIn()` - 创建或更新签到记录
- `createOrUpdateCheckOut()` - 创建或更新签走记录
- `confirmWorkHours()` - 确认工作时数
- `ptConfirmWorkHours()` - PT同工确认
- `updateAttendanceRecord()` - 修改考勤记录

### 路由配置
```javascript
// 在 src/router/index.js 的 hiddenRoutes 中
{
  path: 'attendance',
  hidden: true,
  component: () => import('@/views/workflow/attendance.vue'),
  name: 'WorkflowAttendance',
  meta: {
    title: 'WorkflowAttendance'
  }
}
```

### 页面跳转
从我的轮值表页面点击"签到/签走"按钮跳转：
```javascript
goToAttendance() {
  this.$router.push({
    name: 'WorkflowAttendance',
    query: {
      date: '2025-09-25' // 传递选中的日期
    }
  });
}
```

## 样式特性

### 主题支持
- **PT主题**：淺橙色背景 (#fff3e0)
- **Unit主题**：白色背景 (#ffffff)

### 响应式设计
- 桌面端：横向布局，充分利用屏幕空间
- 移动端：纵向布局，适配小屏幕设备

### 交互反馈
- 按钮状态变化
- 输入验证提示
- 操作成功/失败消息

## 使用流程

1. **进入页面**：从我的轮值表点击"签到/签走"按钮
2. **签到操作**：
   - 选择签到时间（或使用当前时间）
   - 填写备注（可选）
   - 点击"打卡"按钮
3. **签走操作**：
   - 完成签到后，进行签走操作
   - 流程与签到类似
4. **确认工作时数**：
   - 所有节数完成后，输入实际工作时数
   - 点击"确认"按钮
5. **PT确认**（如适用）：
   - PT同工可进行额外确认
   - 生成核实记录

## 注意事项

1. **数据持久化**：所有操作都会调用后端API进行数据保存
2. **错误处理**：包含完整的错误提示和处理机制
3. **权限控制**：根据用户类型显示不同的功能选项
4. **数据验证**：前端和后端双重验证确保数据准确性

## 扩展功能

可以根据需要添加以下功能：
- 批量操作
- 数据导出
- 统计报表
- 消息通知
- 审批流程
