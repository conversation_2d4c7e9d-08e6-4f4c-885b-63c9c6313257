<template>
  <div class="attendance-container" :class="{ 'pt-theme': isPTUser, 'unit-theme': !isPTUser }">
    <div class="content-wrapper">
      <!-- 头部区域 -->
      <div class="header-section">
        <div class="breadcrumb">
          <span class="breadcrumb-link">上班簽到表</span>
          <span class="separator">></span>
          <span class="breadcrumb-link">區梓俊</span>
          <span class="separator">></span>
          <span class="current-page">{{ currentDate }}</span>
        </div>
        <div class="status-indicator">
          <span class="status-text">{{ isCompleted ? '(填寫完畢)' : '(填寫中)' }}</span>
        </div>
      </div>

    <!-- 工作节数容器 -->
    <div class="sessions-container">
      <!-- 第一节 -->
      <div class="session-section">
        <div class="session-header">
          <h3>第 1 節 (共 2 節) > 09:00 - 13:00 > ABC學校 > 入校導師</h3>
        </div>

        <!-- 第一节签到 -->
        <el-form label-width="130px" class="attendance-form">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="簽到時間">
                <el-date-picker
                  v-if="session1.checkIn.isEditing"
                  v-model="session1.checkIn.time"
                  type="datetime"
                  format="yyyy/MM/dd HH:mm"
                  value-format="yyyy/MM/dd HH:mm"
                  size="small"
                  style="width: 100%"
                />
                <span v-else class="time-display">{{ session1.checkIn.time || 'N/A' }}</span>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="紀錄創建時間">
                <span class="creator-display">{{ session1.checkIn.recordTime || 'N/A' }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row v-if="session1.checkIn.isEditing" :gutter="20">
            <el-col :span="24">
              <el-form-item>
                <el-checkbox v-model="session1.checkIn.absent">因事缺席 (請於備註輸入原因)</el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="創建者">
                <span class="creator-display">{{ session1.checkIn.creator || 'N/A' }}</span>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="備註">
                <el-input
                  v-if="session1.checkIn.isEditing"
                  v-model="session1.checkIn.remark"
                  placeholder="請輸入備註"
                  size="small"
                />
                <span v-else class="remark-display">{{ session1.checkIn.remark || '' }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24" style="text-align: right;">
              <el-button
                v-if="!session1.checkIn.isEditing"
                type="primary"
                size="small"
                @click="editSession1CheckIn"
              >
                {{ session1.checkIn.hasRecord ? '修改' : '打卡' }}
              </el-button>
              <el-button
                v-else
                type="success"
                size="small"
                @click="saveSession1CheckIn"
              >
                儲存
              </el-button>
            </el-col>
          </el-row>
        </el-form>

        <!-- 第一节签走 -->
        <el-form class="attendance-form">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="簽走時間">
                <el-date-picker
                  v-if="session1.checkOut.isEditing"
                  v-model="session1.checkOut.time"
                  type="datetime"
                  format="yyyy/MM/dd HH:mm"
                  value-format="yyyy/MM/dd HH:mm"
                  size="small"
                  style="width: 100%"
                />
                <span v-else class="time-display">{{ session1.checkOut.time || 'N/A' }}</span>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="紀錄創建時間">
                <span class="creator-display">{{ session1.checkOut.recordTime || 'N/A' }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row v-if="session1.checkOut.isEditing" :gutter="20">
            <el-col :span="24">
              <el-form-item>
                <el-checkbox v-model="session1.checkOut.absent">因事缺席 (請於備註輸入原因)</el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="創建者">
                <span class="creator-display">{{ session1.checkOut.creator || 'N/A' }}</span>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="備註">
                <el-input
                  v-if="session1.checkOut.isEditing"
                  v-model="session1.checkOut.remark"
                  placeholder="請輸入備註"
                  size="small"
                />
                <span v-else class="remark-display">{{ session1.checkOut.remark || '' }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24" style="text-align: right;">
              <el-button
                v-if="!session1.checkOut.isEditing"
                type="primary"
                size="small"
                @click="editSession1CheckOut"
              >
                {{ session1.checkOut.hasRecord ? '修改' : '打卡' }}
              </el-button>
              <el-button
                v-else
                type="success"
                size="small"
                @click="saveSession1CheckOut"
              >
                儲存
              </el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 分割线 -->
      <div class="section-divider"></div>

      <!-- 第二节 -->
      <div class="session-section">
        <div class="session-header">
          <h3>第 2 節 (共 2 節) > 18:00 - 21:00 > ABC學校 > 入校導師</h3>
        </div>

        <!-- 第二节签到 -->
        <el-form label-width="130px" class="attendance-form">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="簽到時間">
                <el-date-picker
                  v-if="session2.checkIn.isEditing"
                  v-model="session2.checkIn.time"
                  type="datetime"
                  format="yyyy/MM/dd HH:mm"
                  value-format="yyyy/MM/dd HH:mm"
                  size="small"
                  style="width: 100%"
                />
                <span v-else class="time-display">{{ session2.checkIn.time || 'N/A' }}</span>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="紀錄創建時間">
                <span class="creator-display">{{ session2.checkIn.recordTime || 'N/A' }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row v-if="session2.checkIn.isEditing" :gutter="20">
            <el-col :span="24">
              <el-form-item>
                <el-checkbox v-model="session2.checkIn.absent">因事缺席 (請於備註輸入原因)</el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="創建者">
                <span class="creator-display">{{ session2.checkIn.creator || 'N/A' }}</span>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="備註">
                <el-input
                  v-if="session2.checkIn.isEditing"
                  v-model="session2.checkIn.remark"
                  placeholder="請輸入備註"
                  size="small"
                />
                <span v-else class="remark-display">{{ session2.checkIn.remark || '' }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24" style="text-align: right;">
              <el-button
                v-if="!session2.checkIn.isEditing"
                type="primary"
                size="small"
                @click="editSession2CheckIn"
              >
                {{ session2.checkIn.hasRecord ? '修改' : '打卡' }}
              </el-button>
              <el-button
                v-else
                type="success"
                size="small"
                @click="saveSession2CheckIn"
              >
                儲存
              </el-button>
            </el-col>
          </el-row>
        </el-form>

        <!-- 第二节签走 -->
        <el-form class="attendance-form">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="簽走時間">
                <el-date-picker
                  v-if="session2.checkOut.isEditing"
                  v-model="session2.checkOut.time"
                  type="datetime"
                  format="yyyy/MM/dd HH:mm"
                  value-format="yyyy/MM/dd HH:mm"
                  size="small"
                  style="width: 100%"
                />
                <span v-else class="time-display">{{ session2.checkOut.time || 'N/A' }}</span>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="紀錄創建時間">
                <span class="creator-display">{{ session2.checkOut.recordTime || 'N/A' }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row v-if="session2.checkOut.isEditing" :gutter="20">
            <el-col :span="24">
              <el-form-item>
                <el-checkbox v-model="session2.checkOut.absent">因事缺席 (請於備註輸入原因)</el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="創建者">
                <span class="creator-display">{{ session2.checkOut.creator || 'N/A' }}</span>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="備註">
                <el-input
                  v-if="session2.checkOut.isEditing"
                  v-model="session2.checkOut.remark"
                  placeholder="請輸入備註"
                  size="small"
                />
                <span v-else class="remark-display">{{ session2.checkOut.remark || '' }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24" style="text-align: right;">
              <el-button
                v-if="!session2.checkOut.isEditing"
                type="primary"
                size="small"
                @click="editSession2CheckOut"
              >
                {{ session2.checkOut.hasRecord ? '修改' : '打卡' }}
              </el-button>
              <el-button
                v-else
                type="success"
                size="small"
                @click="saveSession2CheckOut"
              >
                儲存
              </el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <!-- 分割线 -->
    <div class="section-divider"></div>

    <!-- 确认工作时数和核实记录部分 -->
    <div class="work-hours-verification-section">
      <h3>確認工作時數</h3>
      <div class="work-hours-description">
        必須有完整打卡紀錄，方可確認實際工作時數，如打卡紀錄一經修改，必須重新核實實際工作時數。
      </div>

      <el-form :model="workHoursForm" label-width="130px" class="work-hours-form">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
            <el-form-item label="本日已編更時數">
              <el-input
                v-model="scheduledWorkHours"
                readonly
                size="small"
                suffix-icon="el-icon-time"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="16" :lg="16" :xl="16">
            <el-form-item label="參考工作時數">
              <div class="reference-hours">
                <span v-if="!allSessionsCompleted" class="reference-text">未有完整打卡紀錄，未能計算</span>
                <span v-else class="reference-text">{{ calculatedWorkHours }} 小時</span>
                <div class="reference-note">此為以實到離退時間計算之參考時數</div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
            <el-form-item label="實際工作時數">
              <el-input-number
                v-model="actualWorkHours"
                :min="0"
                :max="24"
                :step="0.25"
                :precision="2"
                :disabled="!canConfirmWorkHours"
                size="small"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="10" :lg="10" :xl="10">
            <el-form-item label="備註">
              <el-input
                v-model="workHoursRemark"
                placeholder="如實際工作時數少於已編更時數，請於備註輸入原因。"
                type="textarea"
                :rows="2"
                size="small"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="6" :lg="6" :xl="6">
            <el-form-item label=" ">
              <el-button type="primary" size="small" @click="confirmWorkHours" style="width: 100%;">確認</el-button>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="核實者">
              <el-input
                v-model="verifier"
                readonly
                size="small"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="核實時間">
              <el-input
                v-model="verifyTime"
                readonly
                size="small"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label=" " v-if="canPTConfirm">
              <el-button
                type="warning"
                size="small"
                @click="ptConfirm"
              >
                兼職/臨時工確認
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="section-divider"></div>

    <!-- 核实记录 -->
    <div class="verification-part">
      <h3>核實紀錄</h3>
      <el-form :model="verificationRecord" label-width="130px" class="verification-form">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="同工核實">
              <el-input
                :value="verificationRecord?.verifiedBy || ''"
                readonly
                size="small"
                placeholder="未核實"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="時間">
              <el-input
                :value="verificationRecord?.verifiedTime || ''"
                readonly
                size="small"
                placeholder="N/A"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="負責人核實">
              <el-input
                :value="verificationRecord?.supervisorVerified || ''"
                readonly
                size="small"
                placeholder="未核實"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="時間">
              <el-input
                :value="verificationRecord?.supervisorVerifiedTime || ''"
                readonly
                size="small"
                placeholder="N/A"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="備註">
              <el-input
                :value="verificationRecord?.remark || ''"
                type="textarea"
                :rows="2"
                readonly
                size="small"
                placeholder="無備註"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    </div>
  </div>
</template>

<script>
import * as AttendanceApi from '@/api/workflow/attendance'

export default {
  data() {
    return {
      // 用户类型
      isPTUser: true, // 从用户信息获取
      userType: 'PT', // PT 或 Unit

      // 当前日期
      currentDate: '2025/09/25',

      // 总节数
      totalSessions: 2,

      // 是否已完成
      isCompleted: false,

      // 第一节数据
      session1: {
        checkIn: {
          time: '2025/09/25 09:00',
          recordTime: '2025/09/25 09:14',
          creator: '區梓俊',
          remark: '已到公司，但遲了打卡',
          absent: false,
          hasRecord: true,
          isEditing: false
        },
        checkOut: {
          time: '2025/09/25 13:14',
          recordTime: '2025/09/25 13:14',
          creator: '區梓俊',
          remark: '',
          absent: false,
          hasRecord: true,
          isEditing: false
        }
      },

      // 第二节数据
      session2: {
        checkIn: {
          time: '2025/09/25 17:33',
          recordTime: '2025/09/25 17:33',
          creator: '區梓俊',
          remark: '',
          absent: false,
          hasRecord: true,
          isEditing: false
        },
        checkOut: {
          time: '2025/09/25 21:00',
          recordTime: '2025/09/25 22:33',
          creator: '區梓俊',
          remark: '',
          absent: false,
          hasRecord: true,
          isEditing: false
        }
      },

      // 工作时数相关
      scheduledWorkHours: 8, // 已编更时数
      actualWorkHours: 7.68, // 实际工作时数
      workHoursRemark: '', // 工作时数备注
      calculatedWorkHours: 7.68, // 计算的工作时数
      verifier: '區梓俊', // 核实者
      verifyTime: '2025/09/25 22:34', // 核实时间

      // 核实记录
      verificationRecord: {
        verifiedBy: '區',
        verifiedTime: '2025/09/25 22:34',
        supervisorVerified: '',
        supervisorVerifiedTime: '',
        remark: ''
      },

      // 工作时数表单
      workHoursForm: {}
    }
  },

  computed: {
    // 是否所有节数都已完成打卡
    allSessionsCompleted() {
      return this.session1.checkIn.hasRecord &&
             this.session1.checkOut.hasRecord &&
             this.session2.checkIn.hasRecord &&
             this.session2.checkOut.hasRecord
    },

    // 是否可以确认工作时数
    canConfirmWorkHours() {
      return this.allSessionsCompleted
    },

    // 是否可以PT确认
    canPTConfirm() {
      return this.userType === 'PT' && this.canConfirmWorkHours
    }
  },
  methods: {
    // 第一节签到编辑
    editSession1CheckIn() {
      this.session1.checkIn.isEditing = true
    },

    // 第一节签到保存
    async saveSession1CheckIn() {
      // 验证因事缺席必须有备注
      if (this.session1.checkIn.absent && !this.session1.checkIn.remark) {
        this.$message.error('請於備註輸入缺席原因')
        return
      }

      // 验证签到时间
      if (!this.session1.checkIn.absent && !this.session1.checkIn.time) {
        this.session1.checkIn.time = new Date().toISOString().slice(0, 19).replace('T', ' ')
      }

      try {
        const data = {
          sessionId: 1,
          checkInTime: this.session1.checkIn.absent ? null : this.session1.checkIn.time,
          remark: this.session1.checkIn.remark,
          isAbsent: this.session1.checkIn.absent
        }

        await AttendanceApi.createOrUpdateCheckIn(data)
        this.session1.checkIn.recordTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        this.session1.checkIn.hasRecord = true
        this.session1.checkIn.isEditing = false

        this.$message.success('第一節簽到記錄已更新')
      } catch (error) {
        this.$message.error('保存失敗: ' + error.message)
      }
    },

    // 第一节签走编辑
    editSession1CheckOut() {
      this.session1.checkOut.isEditing = true
    },

    // 第一节签走保存
    async saveSession1CheckOut() {
      // 验证因事缺席必须有备注
      if (this.session1.checkOut.absent && !this.session1.checkOut.remark) {
        this.$message.error('請於備註輸入缺席原因')
        return
      }

      // 验证签走时间
      if (!this.session1.checkOut.absent && !this.session1.checkOut.time) {
        this.session1.checkOut.time = new Date().toISOString().slice(0, 19).replace('T', ' ')
      }

      try {
        const data = {
          sessionId: 1,
          checkOutTime: this.session1.checkOut.absent ? null : this.session1.checkOut.time,
          remark: this.session1.checkOut.remark,
          isAbsent: this.session1.checkOut.absent
        }

        await AttendanceApi.createOrUpdateCheckOut(data)
        this.session1.checkOut.recordTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        this.session1.checkOut.hasRecord = true
        this.session1.checkOut.isEditing = false

        this.$message.success('第一節簽走記錄已更新')
      } catch (error) {
        this.$message.error('保存失敗: ' + error.message)
      }
    },

    // 第二节签到编辑
    editSession2CheckIn() {
      this.session2.checkIn.isEditing = true
    },

    // 第二节签到保存
    async saveSession2CheckIn() {
      // 验证因事缺席必须有备注
      if (this.session2.checkIn.absent && !this.session2.checkIn.remark) {
        this.$message.error('請於備註輸入缺席原因')
        return
      }

      // 验证签到时间
      if (!this.session2.checkIn.absent && !this.session2.checkIn.time) {
        this.session2.checkIn.time = new Date().toISOString().slice(0, 19).replace('T', ' ')
      }

      try {
        const data = {
          sessionId: 2,
          checkInTime: this.session2.checkIn.absent ? null : this.session2.checkIn.time,
          remark: this.session2.checkIn.remark,
          isAbsent: this.session2.checkIn.absent
        }

        await AttendanceApi.createOrUpdateCheckIn(data)
        this.session2.checkIn.recordTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        this.session2.checkIn.hasRecord = true
        this.session2.checkIn.isEditing = false

        this.$message.success('第二節簽到記錄已更新')
      } catch (error) {
        this.$message.error('保存失敗: ' + error.message)
      }
    },

    // 第二节签走编辑
    editSession2CheckOut() {
      this.session2.checkOut.isEditing = true
    },

    // 第二节签走保存
    async saveSession2CheckOut() {
      // 验证因事缺席必须有备注
      if (this.session2.checkOut.absent && !this.session2.checkOut.remark) {
        this.$message.error('請於備註輸入缺席原因')
        return
      }

      // 验证签走时间
      if (!this.session2.checkOut.absent && !this.session2.checkOut.time) {
        this.session2.checkOut.time = new Date().toISOString().slice(0, 19).replace('T', ' ')
      }

      try {
        const data = {
          sessionId: 2,
          checkOutTime: this.session2.checkOut.absent ? null : this.session2.checkOut.time,
          remark: this.session2.checkOut.remark,
          isAbsent: this.session2.checkOut.absent
        }

        await AttendanceApi.createOrUpdateCheckOut(data)
        this.session2.checkOut.recordTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        this.session2.checkOut.hasRecord = true
        this.session2.checkOut.isEditing = false

        // 第二节是最后一节，提示确认工作时数
        this.$message.info('請於簽走後立即確認工作時數紀錄。')

        this.$message.success('第二節簽走記錄已更新')
      } catch (error) {
        this.$message.error('保存失敗: ' + error.message)
      }
    },

    // 确认工作时数
    async confirmWorkHours() {
      if (this.actualWorkHours > this.scheduledWorkHours) {
        this.$message.error('如實際工作時數已超出編更時數，將無法儲存，請聯繫直屬上司。')
        return
      }

      if (this.actualWorkHours < this.scheduledWorkHours && !this.hasRemarkForLessHours()) {
        this.$message.error('如實際工作時數少於編更時數，請於備註輸入原因。')
        return
      }

      try {
        const data = {
          date: this.currentDate,
          actualWorkHours: this.actualWorkHours,
          scheduledWorkHours: this.scheduledWorkHours,
          sessions: this.workSessions
        }

        await AttendanceApi.confirmWorkHours(data)
        this.$message.success('工作時數確認成功')
      } catch (error) {
        this.$message.error('確認失敗: ' + error.message)
      }
    },

    // PT同工确认
    async ptConfirm() {
      try {
        const data = {
          date: this.currentDate,
          actualWorkHours: this.actualWorkHours,
          verifiedBy: '當前用戶', // 从用户信息获取
          verifiedTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
        }

        await AttendanceApi.ptConfirmWorkHours(data)

        this.verificationRecord = {
          verifiedBy: data.verifiedBy,
          verifiedTime: data.verifiedTime
        }

        this.$message.success('兼職/臨時工確認成功')
      } catch (error) {
        this.$message.error('確認失敗: ' + error.message)
      }
    },

    // 检查是否有备注说明工时少的原因
    hasRemarkForLessHours() {
      return this.workSessions.some(session =>
        session.checkInRemark || session.checkOutRemark
      )
    },

    // 加载考勤数据
    async loadAttendanceData() {
      try {
        // 这里可以调用API获取实际数据
        // const data = await AttendanceApi.getWorkTimeRecord(this.currentDate)
        // 处理返回的数据...
      } catch (error) {
        console.error('加载数据失败:', error)
      }
    }
  },

  mounted() {
    // 页面加载时获取数据
    this.loadAttendanceData()
  }
};
</script>

<style>
/* 主题样式 */
.attendance-container {
  min-height: 100vh;
  padding: 20px;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
}

.content-wrapper {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.pt-theme {
  background-color: #fff3e0; /* 淺橙色背景 */
}

.unit-theme {
  background-color: #ffffff; /* 白色背景 */
}

/* 头部区域 */
.header-section {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  font-size: 16px;
}

.breadcrumb-link {
  color: #409eff;
  cursor: pointer;
  text-decoration: none;
}

.breadcrumb-link:hover {
  text-decoration: underline;
}

.separator {
  margin: 0 8px;
  color: #666;
}

.current-page {
  color: #333;
  font-weight: bold;
}

.status-indicator {
  margin-left: auto;
}

.status-text {
  color: #e6a23c;
  font-weight: bold;
}

/* 工作节数容器 */
.sessions-container {
  background: #fff3e0;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.session-section {
  padding: 20px 0;
}

.session-section:not(:last-child) {
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 20px;
  padding-bottom: 20px;
}

/* 分割线 */
.section-divider {
  height: 1px;
  background: #e0e0e0;
  margin: 20px 0;
}

.session-header h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

/* 考勤部分 */
.attendance-section {
  margin-bottom: 15px;
}

.attendance-form {
  margin-bottom: 10px;
}

.attendance-form .el-form-item {
  margin-bottom: 15px;
}

.attendance-form .el-form-item__label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  line-height: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  width: 120px !important;
  text-align: justify;
  text-align-last: justify;
  padding-right: 10px;
}

.attendance-form .el-form-item__content {
  line-height: 32px;
  min-height: 32px;
  display: flex;
  align-items: center;
}

.time-display {
  font-size: 14px;
  color: #333;
  background: #f5f5f5;
  padding: 8px 12px;
  border-radius: 4px;
  min-height: 32px;
  line-height: 16px;
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.creator-display {
  font-size: 14px;
  color: #333;
  background: #f5f5f5;
  padding: 8px 12px;
  border-radius: 4px;
  min-height: 32px;
  line-height: 16px;
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.remark-display {
  font-size: 14px;
  color: #333;
  background: #f5f5f5;
  padding: 8px 12px;
  border-radius: 4px;
  min-height: 32px;
  line-height: 16px;
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

/* 分割线 */
.section-divider {
  height: 1px;
  background: #eee;
  margin: 20px 0;
}

/* 工作时数确认和核实记录部分 */
.work-hours-verification-section {
  background: #fff3e0;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.work-hours-verification-section h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.work-hours-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
  line-height: 1.5;
}

.work-hours-form {
  margin-top: 20px;
}

.work-hours-form .el-form-item {
  margin-bottom: 20px;
}

.work-hours-form .el-form-item__label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  line-height: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  width: 120px !important;
  text-align: justify;
  text-align-last: justify;
  padding-right: 10px;
}

.work-hours-form .el-form-item__content {
  line-height: 32px;
  min-height: 32px;
  display: flex;
  align-items: center;
}

.reference-hours {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.reference-text {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.reference-note {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
}

/* 核实记录样式 */
.verification-part h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.verification-form {
  margin-top: 20px;
}

.verification-form .el-form-item {
  margin-bottom: 20px;
}

.verification-form .el-form-item__label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  line-height: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  width: 120px !important;
  text-align: justify;
  text-align-last: justify;
  padding-right: 10px;
}

.verification-form .el-form-item__content {
  line-height: 32px;
  min-height: 32px;
  display: flex;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .attendance-container {
    padding: 10px;
  }

  .attendance-row {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .field-group {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .action-btn {
    margin-left: 0;
    align-self: flex-start;
  }
}

/* Element UI 组件自定义样式 */
.el-date-editor {
  width: 100%;
}

.el-input-number {
  width: 120px;
}

.el-checkbox {
  font-size: 14px;
}

/* 确保输入框和文本显示框对齐 */
.el-input__inner,
.el-textarea__inner,
.el-input-number__input {
  height: 32px;
  line-height: 32px;
}

.el-textarea .el-textarea__inner {
  height: auto;
  min-height: 32px;
}

/* 确保日期选择器对齐 */
.el-date-editor .el-input__inner {
  height: 32px;
  line-height: 32px;
}

.el-button {
  min-width: 80px;
}

/* 输入框样式 */
.el-input__inner {
  background-color: #f5f5f5 !important;
  border: 1px solid #ddd !important;
}

.el-input__inner:focus {
  background-color: #fff !important;
  border-color: #409eff !important;
}

.el-input__inner[readonly] {
  background-color: #f5f5f5 !important;
  color: #666 !important;
}

.el-textarea__inner {
  background-color: #f5f5f5 !important;
  border: 1px solid #ddd !important;
}

.el-textarea__inner:focus {
  background-color: #fff !important;
  border-color: #409eff !important;
}

.el-input-number .el-input__inner {
  background-color: #f5f5f5 !important;
  border: 1px solid #ddd !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .attendance-container {
    padding: 10px;
  }

  .content-wrapper {
    max-width: 100%;
  }

  .sessions-container {
    padding: 15px;
  }

  .session-section {
    padding: 15px 0;
  }

  .session-header h3 {
    font-size: 14px;
    line-height: 1.4;
  }

  .attendance-form .el-form-item__label,
  .work-hours-form .el-form-item__label,
  .verification-form .el-form-item__label {
    font-size: 13px;
    width: 100px !important;
    text-align: left !important;
    text-align-last: left !important;
  }

  .time-display,
  .creator-display,
  .remark-display {
    font-size: 13px;
    padding: 6px 10px;
  }

  /* 移动端每个字段占满一行 */
  .el-col {
    margin-bottom: 10px;
  }

  .el-row .el-col:last-child {
    margin-bottom: 0;
  }

  /* 工作时数确认部分移动端优化 */
  .work-hours-verification-section {
    padding: 15px;
  }

  .work-hours-form .el-form-item__label,
  .verification-form .el-form-item__label {
    font-size: 13px;
  }

  /* 确保按钮在移动端全宽显示 */
  .el-button {
    width: 100%;
  }

  /* 参考工作时数文字在移动端的显示 */
  .reference-hours {
    text-align: left;
  }

  .reference-text {
    font-size: 13px;
  }

  .reference-note {
    font-size: 11px;
    margin-top: 5px;
  }
}
</style>