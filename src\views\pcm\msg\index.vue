<template>
  <div class="messages-container">
    <!-- 左侧二级菜单 -->
    <div class="left-menu">
      <div class="menu-title">消息</div>
      <div class="menu-item" :class="{ active: activeMenu === 'cardShare' }" @click="changeMenu('cardShare')">
        <img :src="getIcon('cardShare')" class="menu-icon" />
        名片共享
        <span class="menu-count">{{ counts.cardShare }}</span>
      </div>
      <div class="menu-item" :class="{ active: activeMenu === 'taskCollaboration' }" @click="changeMenu('taskCollaboration')">
        <img :src="getIcon('taskCollaboration')" class="menu-icon" />
        任务协作
        <span class="menu-count">{{ counts.taskCollaboration }}</span>
      </div>
      <div class="menu-item" :class="{ active: activeMenu === 'pendingColleagues' }" @click="changeMenu('pendingColleagues')">
        <img :src="getIcon('pendingColleagues')" class="menu-icon" />
        待加入的同事
        <span class="menu-count">{{ counts.pendingColleagues }}</span>
      </div>
      <div class="menu-item" :class="{ active: activeMenu === 'cardUpload' }" @click="changeMenu('cardUpload')">
        <img :src="getIcon('cardUpload')" class="menu-icon" />
        名片上傳
        <span class="menu-count">{{ counts.cardUpload }}</span>
      </div>
    </div>

    <!-- 右侧内容 -->
    <div class="right-content">
      <div class="content-title">{{ activeMenuLabel }}</div>
      <div class="content-divider"></div>
      <div class="content-body">
        <!-- 内容区域暂时留空 -->
        <p v-if="activeMenu === 'cardShare'">名片共享内容待完善...</p>
        <p v-if="activeMenu === 'taskCollaboration'">任务协作内容待完善...</p>
        <p v-if="activeMenu === 'pendingColleagues'">待加入的同事内容待完善...</p>
        <p v-if="activeMenu === 'cardUpload'">名片上傳内容待完善...</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      activeMenu: 'cardShare', // 默认激活的菜单
      counts: {
        cardShare: 5, // 名片共享数量
        taskCollaboration: 3, // 任务协作数量
        pendingColleagues: 2, // 待加入的同事数量
        cardUpload: 7, // 名片上傳数量
      },
    };
  },
  computed: {
    activeMenuLabel() {
      switch (this.activeMenu) {
        case 'cardShare':
          return '名片共享';
        case 'taskCollaboration':
          return '任务协作';
        case 'pendingColleagues':
          return '待加入的同事';
        case 'cardUpload':
          return '名片上傳';
        default:
          return '';
      }
    },
  },
   mounted() {
    this.activeMenu = this.$route.query.menuName; 
  },
  methods: {
    changeMenu(menu) {
      this.activeMenu = menu;
    },
    getIcon(menu) {
      // 根据菜单项返回对应的图标路径
      switch (menu) {
        case 'cardShare':
          return require('@/assets/pcm/msg/icon-card-share.png');
        case 'taskCollaboration':
          return require('@/assets/pcm/msg/icon-task-collaboration.png');
        case 'pendingColleagues':
          return require('@/assets/pcm/msg/icon-pending-colleagues.png');
        case 'cardUpload':
          return require('@/assets/pcm/msg/icon-card-upload.png');
        default:
          return '';
      }
    },
  },
};
</script>

<style scoped>
.messages-container {
  display: flex;
  height: 100%;
  background-color: #f0f4fa;
}

.left-menu {
  width: 25%;
  background-color: white;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.menu-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between; /* 让内容分布在两端 */
  padding: 10px;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 10px;
  font-size: 14px;
  color: #1f1f1f;
  font-weight: 400;
}

.menu-item:hover {
  background-color: #ed6c001a;
  color: #ed6c00;
}

.menu-item.active {
  background-color: #ed6c001a;
  color: #ed6c00;
}

.menu-icon {
  width: 16px;
  height: 16px;
  margin-right: 10px;
  filter: brightness(0) saturate(100%) invert(0%) sepia(0%) saturate(7500%) hue-rotate(0deg) brightness(100%) contrast(100%);
}

.menu-item:hover .menu-icon,
.menu-item.active .menu-icon {
  filter: brightness(0) saturate(100%) invert(50%) sepia(100%) saturate(1000%) hue-rotate(0deg) brightness(100%) contrast(100%);
}

.menu-count {
  font-size: 12px;
  color: #666; /* 设置文字颜色为灰色 */
  margin-left: auto; /* 将计数推到最右侧 */
}

.right-content {
  width: 75%;
  margin-left: 10px;
  background-color: white;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.content-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}

.content-divider {
  height: 1px;
  background-color: #e0e0e0;
  margin-bottom: 20px;
}

.content-body {
  color: #666;
}
</style>