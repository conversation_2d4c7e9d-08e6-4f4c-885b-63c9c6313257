<template>
  <div class="demo-container">
    <div class="demo-controls">
      <h3>演示控制</h3>
      <el-switch
        v-model="isPTUser"
        active-text="PT同事"
        inactive-text="Unit同事">
      </el-switch>
    </div>
    
    <!-- 引入我的輪值表組件 -->
    <MyCalendar :is-pt-user="isPTUser" />
  </div>
</template>

<script>
import MyCalendar from './mycalendar.vue'

export default {
  name: 'CalendarDemo',
  components: {
    MyCalendar
  },
  data() {
    return {
      isPTUser: true
    }
  }
}
</script>

<style scoped>
.demo-container {
  padding: 20px;
}

.demo-controls {
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.demo-controls h3 {
  margin: 0 0 15px 0;
  color: #333;
}
</style>
